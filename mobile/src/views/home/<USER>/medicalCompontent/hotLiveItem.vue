<!-- 热门直播的每一项 -->
<template>
  <div class="hotLiveItem" :style="{ marginTop: isSearch ? '0px' : '10px' }">
    <div class="hotLiveItemContent">
      <div class="flex-left">
        <div class="item-left" @click="linkUrl(itemData)">
          <div v-if="itemData.totalPaymentType == 0 && itemData.itemType !== 'activity' && currentPlatformId != 3" class="item-left-payType">免费</div>
          <img class="item-left-img" :src="itemData.coverImg || itemData.bannerImg" alt="" />
          <!-- 针对医疗的直播，需要显示直播状态 -->
          <live-status
            :info="itemData"
            v-if="currentPlatformId == 3"
            layout="bottom-right"
            :liveChargeTypeStyle="{ marginBottom: '4px' }"
            :liveTypeStyle="{ marginBottom: '4px', marginRight: '4px' }"
          ></live-status>
        </div>
        <div
          :class="['item-right', 'unit', { 'border-1px': isSearch }, 'flex-vertical']"
          :style="{ paddingBottom: isSearch && isShowTag ? '10px' : isSearch && !isShowTag ? '15px' : '0', overflow: 'hidden' }"
        >
          <div class="item-right-top" @click="linkUrl(itemData)">
            {{ itemData.name || itemData.activityName }}
          </div>
          <!-- 针对医疗添加索引文字 -->
          <div class="item-right-index" v-if="currentPlatformId == 3">
            <span class="item-text">{{ itemData.subName }}</span>
          </div>
          <div class="unit"></div>
          <div class="item-right-middle" @click="linkUrl(itemData)">
            <span class="item-text">{{ itemData.beginTime ? moment(itemData.beginTime, 1, "/", "/") : "" }}</span>
            <span class="item-text right_bizName" v-if="currentPlatformId != 3">{{ itemData.sponsorName || itemData.organizerName }}</span>
          </div>
          <div class="unit"></div>
          <div class="item-right-bottom flex-left" v-if="isShowTag && currentPlatformId != 3">
            <div class="infoRecommend-detail">
              <template v-if="currentPlatformId != 3">
                <div class="tagDetail" :key="indexs + 'tag'" v-if="tag" :style="'background: rgba(90, 46, 132, 0.1);color:rgba(90, 46, 132, 1)'" v-for="(tag, indexs) in itemData.channelList">
                  {{ tag }}
                </div>
                <div class="tagDetail" :key="indexs + 'typeTag'" v-if="tag" :style="'background: rgba(0, 137, 134, 0.2);color:rgba(0, 137, 134, 1)'" v-for="(tag, indexs) in itemData.typeList">
                  {{ tag }}
                </div>
                <div class="tagDetail" :key="indexs" v-if="tag" :style="'background: rgba(243, 151, 1, 0.1);color:rgba(243, 151, 1, 1)'" v-for="(tag, indexs) in itemData.classifyList">
                  {{ tag }}
                </div>
              </template>
            </div>
            <div class="item-ellipsis unit-0" v-if="isShowEllipsis" @click.stop="expandContent(index)">...</div>
          </div>
          <!-- 新闻 -->
          <!-- <liveItemNews v-if="isSearch" :itemData="itemData" from="rowOne"></liveItemNews> -->
        </div>
      </div>
      <!-- 针对医疗做的处理 -->
      <div class="item-right-medical" v-if="currentPlatformId == 3">
        <div class="item-right-medical-content flex-right">
          <div class="item-right-medical-content-btn" @click="linkUrl(itemData, 'watch')">立即{{ itemData.itemType == "activity" ? "报名" : "观看" }}</div>
        </div>
      </div>
      <!-- 新闻 -->
      <!-- <liveItemNews v-if="!isSearch" :itemData="itemData" from="rowOne"></liveItemNews> -->
    </div>
  </div>
</template>
<script>
import liveItemNews from "./liveListStyle/liveItemNews";
import liveStatus from "@/components/common/liveStatus.vue";
export default {
  name: "",
  props: ["itemData", "isSearch", "index"],
  data() {
    return {
      isShowEllipsis: false, // 是否显示右边的省略
      platformId: this.$route.query.platformId,
    };
  },

  created() {},

  components: {},

  computed: {
    isShowTag() {
      return (
        (this.itemData.channelList && this.itemData.channelList.length) ||
        (this.itemData.classifyList && this.itemData.classifyList.length) ||
        (this.itemData.typeList && this.itemData.typeList.length)
      );
    },
  },
  mounted() {},
  methods: {
    linkUrl(item, type) {
      if (item.itemType == "activity") {
        item.id = item.activityId;
        this.naviToDetails(this.$route.query.platformId, item.itemType, item);
      } else {
        if (type == "watch") {
          window.location.href =
            location.origin + `/mobile/#/zh-cn/continuousLive?liveId=${item.itemId ? item.itemId : item.id}&platformId=${this.$route.query.platformId}&bizId=${item.bizId}&domainType=1&action=enterLive`;
        } else {
          item.id = item.itemId;
          this.naviToDetails(this.$route.query.platformId, item.itemType, item);
        }
      }
    },
    expandContent() {
      this.$emit("changeExpand", this.index);
    },
  },
  components: {
    liveItemNews,
    liveStatus,
  },
};
</script>
<style scoped lang="scss">
.hotLiveItem {
  background: #ffffff;
  border-radius: 4px;
  box-shadow: 0px 4px 10px 0px rgba(0, 0, 0, 0.05);
  .hotLiveItemContent {
    padding: 10px;
    .item-left {
      width: 120px;
      height: 72px;
      margin-right: 10px;
      flex-shrink: 0;
      position: relative;
      display: flex;
      flex-direction: column;
      align-items: flex-end;
      .item-left-payType {
        position: absolute;
        top: 2px;
        left: 2px;
        width: 40px;
        height: 20px;
        background: rgba(0, 0, 0, 0.4);
        color: #fff;
        border-radius: 2px;
        font-size: 14px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        line-height: 20px;
        text-align: center;
      }
      .item-left-img {
        width: 100%;
        height: 72px;
        border-radius: 4px;
        object-fit: contain;
      }
    }
    .border-1px {
      border-bottom: 1px solid #eee;
    }
    .item-right {
      .item-right-top {
        height: 36px;
        font-size: 14px;
        font-weight: 500;
        color: #333333;
        line-height: 18px;
        text-overflow: -o-ellipsis-lastline;
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        line-clamp: 2;
        -webkit-box-orient: vertical;
      }
      .item-right-middle {
        margin-top: 6px;
        display: flex;
        justify-content: space-between;
        .item-text {
          font-size: 12px;
          font-family: PingFangSC-Regular, PingFang SC;
          font-weight: 400;
          color: #666666;
          margin-right: 9px;
        }
        .right_bizName {
          float: right;
          margin-right: 0;
          max-width: 115px;
          height: 12px;
          overflow: hidden;
          text-overflow: ellipsis;
          display: -webkit-box;
          -webkit-line-clamp: 1;
          -webkit-box-orient: vertical;
        }
      }
      .item-right-bottom {
        margin-top: 7px;
        .infoRecommend-detail {
          display: flex;
          justify-content: space-between;
          flex-direction: row;
          /* align-items:flex-end; */
          overflow: hidden;
          text-overflow: ellipsis;
          display: -webkit-box;
          -webkit-line-clamp: 1;
          -webkit-box-orient: vertical;
          height: 16px;
          font-size: 11px;
          .tagDetail {
            margin-right: 6px;
            margin-bottom: 5px;
            padding: 0 6px;
            line-height: 16px;
            display: inline-block;
            border-radius: 1px;
          }
        }
        .item-tags {
          height: 16px;
          overflow: hidden;
          .tag-content {
            .tag-item {
              background: rgba(235, 123, 33, 0.1);
              color: rgba(235, 123, 33, 1);
              display: inline-block;
              margin-bottom: 10px;
              padding: 2px 6px;
              border-radius: 1px;
              font-size: 11px;
              font-family: PingFangSC-Regular, PingFang SC;
              font-weight: 400;
              margin-right: 6px;
            }
            .tag-item-classify {
              background: rgba(20, 100, 161, 0.1);
              color: #1464a1;
              display: inline-block;
              margin-bottom: 10px;
              padding: 2px 6px;
              border-radius: 1px;
              font-size: 11px;
              font-family: PingFangSC-Regular, PingFang SC;
              font-weight: 400;
              margin-right: 6px;
            }
            .tag-item-type {
              background: rgba(0, 137, 134, 0.2);
              color: rgba(0, 137, 134, 1);
              display: inline-block;
              margin-bottom: 10px;
              padding: 2px 6px;
              border-radius: 1px;
              font-size: 11px;
              font-family: PingFangSC-Regular, PingFang SC;
              font-weight: 400;
              margin-right: 6px;
            }
          }
        }
        .expand {
          height: auto;
          overflow: auto;
        }
        .item-ellipsis {
          width: 9px;
          height: 16px;
          font-size: 11px;
          font-family: PingFangSC-Regular, PingFang SC;
          font-weight: 400;
          color: #777777;
          margin-left: 4px;
          vertical-align: bottom;
        }
      }
      .item-right-index {
        margin-top: 4px;
        text-overflow: ellipsis;
        overflow: hidden;
        white-space: nowrap;
        .item-text {
          font-weight: 400;
          font-size: 12px;
          color: #666666;
          line-height: 14px;
        }
      }
    }
    .item-right-medical {
      margin-top: 6px;
      border-top: 1px solid #eee;
      .item-right-medical-content {
        margin-top: 6px;
        .item-right-medical-content-btn {
          width: fit-content;
          font-weight: 400;
          font-size: 12px;
          color: #ffffff;
          line-height: 16px;
          padding: 5px 10px;
          background: var(--color-primary);
          border-radius: 2px;
        }
      }
    }
  }
}
</style>
