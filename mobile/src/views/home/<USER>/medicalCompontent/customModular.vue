<template>
  <div class="custom-modular">
    <div class="custom-type" v-if="pcConfiguration.showType != 1 && pcConfiguration.showType != 2">
      <div class="recommend" v-if="list.length || isPreviewEnviroment">
        <div class="title-row">
          <div class="recommend-title">
            <span>{{ configuration.name || configuration.subMenuName }}</span>
          </div>
          <div class="change-data" @click="changeData" v-if="!(lastLayoutId == configuration[layoutIdField] && infiniteLoadLast == 1) && pcConfiguration.activityType != 'all'">
            <span>更多</span>
            <img style="width: 5px; height: 10px" src="@/assets/img/right1.png" alt="" />
          </div>
        </div>
        <liveListStyle :list="list" :listStyle="configurationParam.listStyle"></liveListStyle>
      </div>
    </div>
    <classification v-else :configuration="configuration" />
  </div>
</template>

<script>
import liveListStyle from "./liveListStyle/liveListStyle";
import { getHomePageItemList, getHomePageItemList1, getHomePageActivityList } from "@/api/medicine/homePage";
import Classification from "./classification";
export default {
  props: ["configuration", "lastLayoutId", "infiniteLoadLast", "dataType", "layoutIdField"],
  data() {
    return {
      configurationParam: {},
      list: [],
      // 添加pc配置
      pcConfiguration: {},
    };
  },

  created() {},

  components: {
    liveListStyle,
    Classification,
  },
  watch: {
    configuration: {
      handler(val) {
        this.configurationParam = this.configuration.param ? JSON.parse(this.configuration.param) : {};
        this.pcConfiguration = this.configuration.pcParam ? JSON.parse(this.configuration.pcParam) : {};
        if (this.pcConfiguration.showType != 1 && this.pcConfiguration.showType != 2) {
          this.getHomePageItemList();
        }
      },
      deep: true,
      immediate: true,
    },
  },
  computed: {},

  mounted() {},

  methods: {
    getHomePageItemList() {
      let fn;
      let params;
      if (this.configuration.previewApiUrl || this.configuration.apiUrl) {
        fn = getHomePageItemList;
        params = this.isPreviewEnviroment ? this.configuration.previewApiUrl : this.configuration.apiUrl;
      } else {
        fn = this.dataType == "activity" ? getHomePageActivityList : getHomePageItemList1;
        params = {
          layoutId: this.configuration.id,
        };
      }
      fn(params).then((res) => {
        if (res.code == this.$successCode) {
          this.list = res.data;
        } else {
          this.$message.error(res.info);
        }
      });
    },
    updataList(data) {
      this.list = this.list.concat(data);
    },
    changeData() {
      //this.$router.push({ name: 'medicineSearch', query: {type: 'custom',layoutId: this.configuration.layoutId}})
      if (this.pcConfiguration.activityType == "activity") {
        location.assign(
          `${window.location.origin}${window.location.pathname}#/medicineActivityList?platformId=${this.$route.query.platformId}&domainType=1`
        );
      } else {
        location.assign(
          `${window.location.origin}${window.location.pathname}#/medicineSearch?platformId=${this.$route.query.platformId}&domainType=1&moduleType=custom&layoutId=${this.configuration.layoutId}`
        );
      }
    },
  },
};
</script>
<style scoped lang="scss">
.recommend {
  margin-bottom: 10px;
  padding: 0px 15px;
  //background: #fff;
  .title-row {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
    padding-top: 10px;
    margin-bottom: 10px;
    span {
      font-size: 18px;
      font-weight: 500;
      color: rgba(51, 51, 51, 1);
      line-height: 25px;
    }
  }
  .recommend-title {
    display: flex;
    align-items: center;
    margin: 0px 0;
    img {
      height: 24px;
      width: 24px;
    }
    span {
      font-size: 18px;
      margin-left: 6px;
      font-weight: 500;
      color: rgba(51, 51, 51, 1);
      line-height: 25px;
    }
  }

  .change-data {
    display: flex;
    align-items: center;
    span {
      font-size: 14px;
      font-weight: 400;
      color: #666666;
      line-height: 17px;
      margin-right: 5px;
    }
    div {
      height: 16px;
      svg {
        height: 16px;
      }
    }
  }
}
</style>
