<!-- 短视频的组件 -->
<template>
  <div class="short-video-wrap">
    <div class="short-video-content" v-if="pcConfiguration.showType != 1">
      <CommonTitle :isShowMore="configuration.fixed == 1 ? true : false" linkType="moduleList/video" :title="configuration.name" :sourceData="videoData" :headerStyle="{ padding: '0' }" />
      <div class="short-video-list flex-wrap flex-left">
        <commonVideo v-for="(item, index) in videoData" :key="index" :item="item" />
      </div>
    </div>
    <classification v-else :configuration="configuration" />
  </div>
</template>
<script>
import CommonTitle from "./commonTitle";
import commonVideo from "@/components/common/commonVideo";
import { getInfoByDynamicUrl } from "@/api/configurable/common";
import Classification from "./classification";
export default {
  name: "shortVideo",
  props: ["configuration"],
  components: {
    CommonTitle,
    commonVideo,
    Classification,
  },
  data() {
    return {
      videoData: [],
      // 添加pc配置
      pcConfiguration: {},
    };
  },
  watch: {
    configuration: {
      handler(val) {
        // 查看pc的配置
        this.pcConfiguration = this.configuration.pcParam ? JSON.parse(this.configuration.pcParam) : {};
        console.log("this.pcConfiguration", this.pcConfiguration.showType);
        if (this.pcConfiguration.showType != 1) {
          this.fetchVideoData();
        }
      },
      deep: true,
      immediate: true,
    },
  },
  mounted() {},
  computed: {},
  methods: {
    async fetchVideoData() {
      const params = this.isPreviewEnviroment ? this.configuration.previewApiUrl : this.configuration.apiUrl;
      const res = await getInfoByDynamicUrl(params);
      if (res.code == this.$successCode) {
        this.videoData = res.data || [];
      } else {
        this.$toast(res.info);
      }
    },
  },
};
</script>
<style scoped lang="scss">
.short-video-wrap {
  // padding: 0 15px;
  .short-video-content {
    padding: 0 15px;
    .short-video-list {
      margin-top: 10px;
      margin-bottom: 10px;
    }
  }
}
</style>
