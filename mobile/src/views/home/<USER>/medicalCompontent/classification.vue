<!-- 直播类型分类 和 自定义专题 公用组件 -->
<template>
  <div class="classification-wrapper">
    <common-title :isShowMore="categoryList.length ? true : false " :title="configuration.name" :sourceData="currentData" :linkType="linkType" :linkQuery="linkQuery" :isAlwaysShow="categoryList.length || isPreviewEnviroment" />
    <div class="classification-content">
      <scroll :scrollX="true" ref="scroll" :data="categoryList">
        <div class="classification-title flex-middle" style="margin-bottom: 10px;">
          <div class="item-title flex-middle" :class="{ 'item-active-text': activeIndex === index }" v-for="(item, index) in categoryList" :key="index" @click="handleCategoryClick(index)">
            <img class="item-title-img" :src="item.icon || 'https://oss.ienmore.com/frontUpload/partUpload/2025-06-13/2d53ed0faaec550021d3a12cefb1a569.png'" alt="" />
            <span class="item-title-text">{{ item.name || item.subMenuName }}</span>
          </div>
        </div>
      </scroll>
      <div class="classification-list">
        <component :is="currentContentComponent.wrapper" :class="currentContentComponent.wrapperClass" v-bind="currentContentComponent.wrapperProps">
          <!-- 单个组件渲染模式 -->
          <component v-if="currentContentComponent.renderMode === 'single'" :is="currentContentComponent.component" :key="`${activeIndex}-${currentData.length}`" v-bind="getSingleComponentProps()" />
          <!-- 列表组件渲染模式 -->
          <component v-else :is="currentContentComponent.component" v-for="(item, index) in currentData" :key="index" v-bind="getItemProps(item, index)" />
        </component>
      </div>
    </div>
  </div>
</template>
<script>
import commonTitle from "./commonTitle";
import TwoInRow from "./liveListStyle/twoInRow";
import Scroll from "@/components/common/scroll";
import { getInfoByDynamicUrl } from "@/api/configurable/common";
import commonVideo from "@/components/common/commonVideo";
import liveListStyle from "./liveListStyle/liveListStyle";
import commonInformation from "@/components/common/commonInformation";
// 组件配置常量
const COMPONENT_CONFIG = {
  // 直播类型配置
  live: {
    wrapper: "div",
    wrapperClass: "component-live",
    component: "liveListStyle",
    props: (data, listStyle) => ({
      list: data,
      listStyle: listStyle,
    }),
    renderMode: "single",
  },
  // 短视频类型配置
  shortVideo: {
    wrapper: "div",
    wrapperClass: "flex-left flex-wrap short-video-list",
    component: "common-video",
    props: (item, index) => ({ item }),
    renderMode: "list",
  },
  // 新闻信息类型配置
  newsInformation: {
    wrapper: "div",
    wrapperClass: "component-news-information",
    component: "common-information",
    props: (item, index) => ({ item }),
    renderMode: "list",
  },
};

export default {
  name: "Classification",
  props: {
    configuration: {
      type: Object,
      default: () => {},
    },
  },
  components: {
    commonTitle,
    TwoInRow,
    Scroll,
    commonVideo,
    liveListStyle,
    commonInformation,
  },
  data() {
    return {
      activeIndex: 0, // 当前选中的分类索引
      categoryList: [],
    };
  },
  mounted() {
    this.getClassificationList();
  },
  computed: {
    // 当前选中分类的数据
    currentData() {
      const activeCategory = this.categoryList[this.activeIndex];
      if (!activeCategory) return [];

      // 获取显示数量配置
      const displayNum = Number(this.param.displayNum);
      const shouldLimitDisplay = [39,1001].includes(this.configuration.subMenu);
      
      // 获取数据源的函数
      const getDataSource = (category) => {
        // 数据源优先级：details > itemList
        const dataSources = ['details', 'itemList'];
        
        for (const sourceKey of dataSources) {
          const data = category[sourceKey];
          if (data && Array.isArray(data) && data.length > 0) {
            return data;
          }
        }
        
        return [];
      };
      
      // 获取数据源
      const dataSource = getDataSource(activeCategory);
      if (!dataSource.length) return [];

      // 根据配置决定是否限制显示数量
      return shouldLimitDisplay && displayNum > 0 
        ? dataSource.slice(0, displayNum) 
        : dataSource;
    },
    linkQuery() {
      return this.configuration.menuKey.includes("classification") ? null : {
        layoutId: this.configuration.layoutId,
        menuKey: this.configuration.menuKey,
      };
    },
    linkType() {
      return this.configuration.menuKey.includes("classification") ? null : "classifySearch";
    },
    // 配置信息
    param() {
      return this.configuration.param ? JSON.parse(this.configuration.param) : {};
    },
    // 获取当前内容类型
    currentContentType() {
      if (!this.configuration || !this.configuration.menuKey) {
        return "live"; // 默认类型
      }

      const menuKey = this.configuration.menuKey;

      if (menuKey.includes("shortVideo")) {
        return "shortVideo";
      } else if (menuKey.includes("newsInformation")) {
        return "newsInformation";
      }

      // 可以轻松扩展新类型判断
      // else if (menuKey.includes("customType")) {
      //   return "customType";
      // }

      return "live"; // 默认返回直播类型
    },
    // 获取当前组件配置
    currentContentComponent() {
      const contentType = this.currentContentType;
      const config = COMPONENT_CONFIG[contentType];

      if (!config) {
        console.warn(`未找到类型 "${contentType}" 的组件配置，使用默认配置`);
        return COMPONENT_CONFIG.live;
      }

      return config;
    },
  },
  methods: {
    // 处理分类点击
    handleCategoryClick(index) {
      this.activeIndex = index;
    },
    async getClassificationList() {
      const params = this.isPreviewEnviroment ? this.configuration.previewApiUrl : this.configuration.apiUrl;
      const res = await getInfoByDynamicUrl(params);
      if (res.code == this.$successCode) {
        this.categoryList = res.data.filter(item => item.status == 1) || [];
      } else {
        this.$toast(res.info);
      }
    },
    /**
     * 获取列表项组件的props
     * @param {Object} item - 数据项
     * @param {number} index - 索引
     */
    getItemProps(item, index) {
      const config = this.currentContentComponent;

      if (!config || !config.props) {
        return {};
      }

      // 如果是函数，调用函数获取props
      if (typeof config.props === "function") {
        return config.props(item, index, this.currentData);
      }

      // 如果是对象，直接返回
      return config.props;
    },
    /**
     * 获取单个组件的props（用于renderMode为single的情况）
     */
    getSingleComponentProps() {
      const config = this.currentContentComponent;

      if (!config || !config.props) {
        return {};
      }

      // 对于single模式，传递完整数据和配置参数
      if (typeof config.props === "function") {
        return config.props(this.currentData, this.param.listStyle);
      }

      return config.props;
    },
  },
};
</script>
<style scoped lang="scss">
.classification-wrapper {
  .classification-content {
    margin-top: 10px;
    padding-left: 15px;
    box-sizing: border-box;
    width: 100%;
    margin-bottom: 20px;
    .classification-title {
      width: max-content; // 让内容宽度自适应，支持横向滚动
      margin-bottom: 10px;
      .item-title {
        background: #f5f6fa;
        border-radius: 2px;
        border: 1px solid #eeeeee;
        padding: 7px 10px;
        margin-right: 8px;
        flex-shrink: 0;
        cursor: pointer;
        transition: all 0.3s ease;
        .item-title-img {
          width: 16px;
          height: 16px;
          margin-right: 4px;
          display: none;
        }
        .item-title-text {
          font-weight: 400;
          font-size: 12px;
          color: #444444;
          line-height: 16px;
        }
      }
      .item-active-text {
        background: linear-gradient(90deg, #ffffff 0%, #f9fcff 100%);
        border: 1px solid #d9eaf4;
        .item-title-img {
          display: block;
        }
        .item-title-text {
          color: var(--color-primary);
        }
      }
    }
    .classification-list {
      padding-right: 15px;
      box-sizing: border-box;

      // 短视频列表样式
      .short-video-list {
        margin-top: 15px;
        margin-bottom: 15px;
      }
      // 组件容器样式
      .component-news-information {
        // 可以为不同组件类型添加特定样式
        margin-top: 10px;
      }
    }
  }
}
</style>
