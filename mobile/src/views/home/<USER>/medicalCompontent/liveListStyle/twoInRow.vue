<template>
  <div>
    <div class="square-2-column">
      <div class="square-2-column-item" @click="clickItem(item)" v-for="item of list" :style="{paddingBottom: !isShowWatchBtn ? '10px' : '0'}">
        <div>
          <div class="info-image">
            <div v-if="item.totalPaymentType == 0 && item.itemType !== 'activity' && currentPlatformId != 3" class="item-left-payType">免费</div>
            <img class="info-image" :src="item.previewPic || item.coverImg || item.coursePic || item.bannerImg" alt="" />
            <liveIngIcon class="liveing" v-if="item.liveInPorgress == 1 && currentPlatformId != 3"></liveIngIcon>
            <!-- 针对医疗的直播 -->
            <live-status :info="item" v-if="currentPlatformId == 3"></live-status>
          </div>
          <div class="info-title">{{ item.activityName || item.courseName || item.name || item.infoTitle }}</div>
          <!-- 医疗的索引文字 -->
          <div class="info-index-text">{{ item.subName}}</div>
        </div>
        <div class="info-detail">
          <span v-if="item.beginTime || item.publishTime || item.createTime">
            {{ formatTime(item.beginTime || item.publishTime || item.createTime, "date") }}
          </span>
          <div class="info-detail-sponsorName" v-if="currentPlatformId != 3">
            <span>{{ item.sponsorName || item.organizerName }} </span>
          </div>
        </div>
        <div class="swiper-block-tag" v-if="currentPlatformId != 3">
          <div class="tagDetail" :key="indexs" :style="'background: rgba(90, 46, 132, 0.1);color:rgba(90, 46, 132, 1)'" v-for="(tag, indexs) in item.channelList">
            {{ tag }}
          </div>
          <div class="tagDetail" :key="indexs + 'typeTag'" :style="'background: rgba(0, 137, 134, 0.2);color:rgba(0, 137, 134, 1)'" v-for="(tag, indexs) in item.typeList">
            {{ tag }}
          </div>
          <div class="tagDetail" :key="indexs + 'tag'" :style="'background: rgba(243, 151, 1, 0.1);color:rgba(243, 151, 1, 1)'" v-for="(tag, indexs) in item.classifyList">
            {{ tag }}
          </div>
        </div>
        <div class="watch-live-btn" v-if="currentPlatformId == 3 && isShowWatchBtn">
          <div class="watch-live-btn-text" @click.stop="clickItem(item, 'watch')">立即{{ item.itemType == 'activity' ? "报名" : "观看" }}</div>
        </div>
        <!-- 新闻 -->
        <!-- <liveItemNews :itemData="item" from="rowTwo"></liveItemNews> -->
      </div>
    </div>
  </div>
</template>

<script>
import liveIngIcon from "../liveIngIcon";
import liveItemNews from "./liveItemNews";
import LiveStatus from "@/components/common/liveStatus.vue";
export default {
  props: {
    list: {
      type: Array,
      default: () => [],
    },
    isShowWatchBtn: {
      type: Boolean,
      default: true,
    },
  },
  data() {
    return {
      platformId: this.$route.query.platformId,
    };
  },

  created() {},

  components: {
    liveIngIcon,
    liveItemNews,
    LiveStatus,
  },

  computed: {},

  mounted() {},

  methods: {
    clickItem(item, type) {
      if (item.itemType == "activity") {
        item.id = item.activityId;
        this.naviToDetails(this.$route.query.platformId, item.itemType, item);
      } else {
        if (type == "watch") {
          window.location.href =
            location.origin + `/mobile/#/zh-cn/continuousLive?liveId=${item.itemId ? item.itemId : item.id}&platformId=${this.$route.query.platformId}&bizId=${item.bizId}&domainType=1&action=enterLive`;
        } else {
          item.id = item.itemId;
          this.naviToDetails(this.$route.query.platformId, item.itemType, item);
        }
      }
    },
  },
};
</script>
<style scoped lang="scss">
.liveing {
  position: absolute;
  top: 0px;
  left: 0px;
}
.square-2-column {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  justify-content: space-between;
  .square-2-column-item {
    width: 166px;
    background: #fff;
    border-radius: 4px;
    box-shadow: 0px 4px 10px 0px rgba(0, 0, 0, 0.05);
    margin-bottom: 10px;
  }
  .info-image {
    width: -webkit-fill-available;
    border-radius: 4px 4px 0 0;
    height: 100px;
    position: relative;
  }
  .info-title {
    margin: 8px 6px 6px 6px;
    line-height: 16px;
    text-overflow: -o-ellipsis-lastline;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    line-clamp: 2;
    font-size: 12px;
    -webkit-box-orient: vertical;
    font-weight: 500;
    height: 32px;
  }
  .info-index-text {
    margin: 0 6px 4px 6px;
    font-weight: 400;
    font-size: 10px;
    color: #666666;
    line-height: 12px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    margin-bottom: 4px;
    height: 12px;
  }
  .no-cover {
    background: #3151bb;
    color: white;
    padding: 5px;
    border-radius: 5px;
  }

  .info-detail {
    margin: 0 6px;
    display: flex;
    flex-direction: row;
    width: 150px;
    justify-content: space-between;
    color: #666;
    font-size: 10px;
    line-height: 12px;
    .info-detail-sponsorName {
      flex: 1;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      text-align: right;
      margin-left: 20px;
    }
  }
  .swiper-block-tag {
    margin: 10px 0;
    height: 16px;
    font-size: 11px;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 1;
    -webkit-box-orient: vertical;
    .tagDetail {
      margin-right: 6px;
      margin-bottom: 5px;
      padding: 0 6px;
      line-height: 16px;
      display: inline-block;
      border-radius: 1px;
    }
  }
  .watch-live-btn {
    padding: 4px 6px 8px 6px;
    box-sizing: border-box;
    .watch-live-btn-text {
      font-weight: 400;
      font-size: 12px;
      color: #ffffff;
      line-height: 26px;
      background: var(--color-primary);
      border-radius: 2px;
      width: 100%;
      height: 26px;
      text-align: center;
      line-height: 26px;
    }
  }
  .price-text {
    font-size: 12px;
    font-weight: 500;
    color: rgba(255, 107, 0, 1);
    line-height: 17px;
    // margin-bottom: 5px;
  }
  .info-tag {
    position: absolute;
    right: 2px;
    bottom: 2px;
    color: white;
    font-size: 10px;
    background-color: rgba(0, 0, 0, 0.5);
    opacity: 0.8;
    border-radius: 1px;
    padding: 3px;
  }
}
.item-left-payType {
  position: absolute;
  top: 2px;
  left: 2px;
  width: 40px;
  height: 20px;
  background: rgba(0, 0, 0, 0.4);
  color: #fff;
  border-radius: 2px;
  font-size: 14px;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  line-height: 20px;
  text-align: center;
}
</style>
