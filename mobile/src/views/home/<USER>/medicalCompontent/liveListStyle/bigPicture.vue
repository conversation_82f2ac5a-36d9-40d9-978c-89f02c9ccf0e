<template>
  <div class="broadcasting">
    <div class="swiperContent" v-for="(item, index) in list" :key="index">
      <div class="swiper-top">
        <div @click="clickHandler(item, index)">
          <a class="flex-vertical" style="display: block; height: 100%; position: relative">
            <img :src="item.coverImg || item.bannerImg" class="img-item" />
            <div v-if="item.totalPaymentType == 0 && item.itemType !== 'activity' && currentPlatformId != 3" class="item-left-payType">免费</div>
            <liveIngIcon class="liveing" v-if="item.liveInPorgress == 1 && currentPlatformId != 3"></liveIngIcon>
            <live-status :info="item" v-if="currentPlatformId == 3"></live-status>
            <div class="slide-title">{{ item.activityName || item.name }}</div>
            <div class="slide-title-index" v-if="currentPlatformId == 3">{{ item.subName }}</div>
            <div class="flex-left flex-middle slide-bottom">
              <span class="slide-text unit">{{ formatTime(item.beginTime) }}</span>
              <span class="slide-text" v-if="currentPlatformId != 3">{{ item.sponsorName || item.organizerName }}</span>
              <!-- 针对医疗的立即观看的按钮 -->
              <div class="slide-text-btn" @click.stop="clickHandler(item, index, 'watch')" v-if="currentPlatformId == 3">立即{{ item.itemType == "activity" ? "报名" : "观看" }}</div>
            </div>
            <!-- 新闻 -->
            <!-- <liveItemNews :itemData="item" from="bigImg"></liveItemNews> -->
          </a>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import infoRecommend from "@/components/medicine/infoRecommend.vue";
import LiveSwiper from "../liveSwiper";
import liveIngIcon from "../liveIngIcon";
import liveItemNews from "./liveItemNews";
import liveStatus from "@/components/common/liveStatus.vue";
export default {
  props: ["list"],
  data() {
    return {
      infoDataLimited: [],
      currentIndex: 0,
    };
  },
  components: {
    infoRecommend,
    LiveSwiper,
    liveIngIcon,
    liveItemNews,
    liveStatus,
  },
  computed: {},
  watch: {
    configuration: {
      handler(val) {},
      deep: true,
      immediate: true,
    },
  },
  mounted() {},
  methods: {
    changePage(index) {
      this.currentIndex = index;
    },
    clickHandler(item, index, type) {
      if (item.itemType == "activity") {
        item.id = item.activityId;
        this.naviToDetails(this.$route.query.platformId, item.itemType, item);
      } else {
        if (type == "watch") {
          window.location.href =
            location.origin + `/mobile/#/zh-cn/continuousLive?liveId=${item.itemId ? item.itemId : item.id}&platformId=${this.$route.query.platformId}&bizId=${item.bizId}&domainType=1&action=enterLive`;
        } else {
          item.id = item.itemId;
          this.naviToDetails(this.$route.query.platformId, item.itemType, item);
        }
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.broadcasting {
  padding: 10px 0px 0px;
  .swiperContent:last-child {
    margin-bottom: 0;
  }
  .swiperContent {
    width: 100%;
    margin-bottom: 15px;
    background: #ffffff;
    border-radius: 4px;
    box-shadow: 0px 4px 10px 0px rgba(0, 0, 0, 0.05);
    .swiper-top {
      padding: 9px 10px;
      box-sizing: border-box;
      position: relative;

      .liveing {
        position: absolute;
        top: 10px;
        left: 10px;
      }
      .img-item {
        width: 100%;
        height: 195px;
        border-radius: 2px;
        position: relative;
      }
      .slide-title {
        font-size: 16px;
        font-weight: 400;
        color: #333333;
        line-height: 18px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        height: 18px;
        margin-top: 9px;
      }
      .slide-bottom {
        margin-top: 6px;
        .slide-text {
          font-size: 12px;
          font-weight: 400;
          color: #999999;
        }
        .slide-text-btn {
          width: fit-content;
          font-weight: 400;
          font-size: 12px;
          color: #ffffff;
          line-height: 16px;
          padding: 5px 10px;
          background: var(--color-primary);
          border-radius: 2px;
          position: relative;
          z-index: 100;
        }
      }
      .dot-content {
        position: absolute;
        left: 0;
        top: 3px;
        height: 3px;
        width: 100%;
        z-index: 100;
        .my-dot {
          width: 4px;
          height: 3px;
          background-color: rgba(20, 100, 161, 0.6);
          display: inline-block;
          margin-right: 4px;
        }
        .active {
          width: 11px;
          background-color: rgba(20, 100, 161, 1);
        }
      }
      .item-left-payType {
        position: absolute;
        top: 13px;
        right: 15px;
        width: 40px;
        height: 20px;
        background: rgba(0, 0, 0, 0.4);
        color: #fff;
        border-radius: 2px;
        font-size: 14px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        line-height: 20px;
        text-align: center;
      }
      .slide-title-index {
        font-weight: 400;
        font-size: 12px;
        color: #999999;
        line-height: 14px;
        text-overflow: ellipsis;
        overflow: hidden;
        white-space: nowrap;
        width: 100%;
        margin-top: 4px;
      }
    }
  }
}
</style>
