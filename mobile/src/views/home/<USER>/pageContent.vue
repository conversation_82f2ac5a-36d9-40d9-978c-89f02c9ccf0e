<template>
  <div id="mainScroll" style="overflow-x: hidden; overflow-y: scroll">
    <cube-scroll
      v-if="newConfig.length"
      class="list-scroll"
      :class="{ popShow: popShow }"
      ref="scroll"
      :data="list"
      :options="infiniteLoadLast == 1 ? options : optionsFalse"
      @pulling-up="selectLoadingItemList"
    >
      <div class="scroll-content" :class="{ 'scroll-content-car': platformId == 5 }">
        <component
          :ref="'component' + index"
          v-for="(item, index) in newConfig"
          :key="index"
          :is="item.menuKey"
          :configuration="item"
          @toMenuPage="toMenuPage"
          :allConfig="allConfig"
          :lastLayoutId="lastLayoutId"
          :infiniteLoadLast="infiniteLoadLast"
          layoutIdField="layoutId"
          @refreshScroll="refreshScroll"
        ></component>
        <div class="PreviewEnviroment" v-if="isPreviewEnviroment"></div>
      </div>
    </cube-scroll>
    <cooperation v-if="cooperationStatus" @close="cooperationStatus = false"></cooperation>
  </div>
</template>
<script>
//引入UI组件
// 张全领版组件
import "./contentComponent";
// 赵李妍版组件
import "./zlyComponent";
// 20210513  易贸医疗，ui
import "./medicalCompontent";
import Cooperation from "@/components/common/cooperation";
import topBanner from "@/components/common/topBanner";
import { getHomePageItemList1, selectLoadingItemList, getHomePageActivityList, getCustomLiveActivityRel, selectLoadingItemActivityList, getInfiniteLoad } from "@/api/medicine/homePage";

import { selectLoadingActivityList } from "@/api/medicine/activity";
export default {
  components: {
    Cooperation,
    topBanner
  },
  props: ["configuration", "allConfig"],
  data() {
    return {
      // UI配置常量
      UI_CONSTANTS: {
        SCROLL_NAV_BAR_KEY: "scrollNavBar",
        SPECIAL_LAYOUT_DESCRIPTIONS: ["检查行业"],
        PAGE_NAMES: {
          MY_PAGE: "我的",
        },
        BACKGROUND_STYLES: {
          MY_PAGE: "#F5F6FA",
          DEFAULT: "linear-gradient( 180deg, rgba(255,255,255,0) 0%, rgba(242,241,246,0.98) 100%, #F2F1F6 100%, rgba(242,241,246,0.98) 100%), #FFFFFF",
        },
        SCROLL_NAV_INSERT_POSITION: 1,
      },

      scrollNavBar: [], // 计算的出是否需要scrollBar
      newConfig: [], // 因要将品类转换为scrollbar，所以重新config
      infiniteLoad: 0,
      refreshScrollTimer: null, // 防抖定时器
      options: {
        // 上拉加载设置
        pullUpLoad: {
          threshold: 30,
          visible: false,
          txt: {
            more: "加载更多数据",
            noMore: "没有更多了！",
          },
        },
        // eventPassthrough: 'vertical'
      },
      optionsFalse: {
        // 上拉加载设置
        pullUpLoad: false,
        // eventPassthrough: 'vertical'
      },
      lastLayoutId: null,
      page: {
        pageNum: 1,
        pageSize: 10,
      },
      list: [],
      itemIdList: [],
      lastLayout: {}, // 最后一个模块
      liveModels: [
        // 直播 相关的模块
        7, // 最新直播
        11, // 热门直播
        12, // 往期直播
        23, // 线上直播
      ],
      activityModels: [
        // 活动 相关的模块
        15, // 即将召开
        16, // 热门会展
        24, // 线下会议
        28, // 往期回顾
      ],
      liveActivityModels: [
        // 直播+活动 相关的模块
        1001, // 自定义活动模块（直播+活动）
      ],
      popShow: false, // 微信弹框显示
      // 控制商务合作弹窗
      cooperationStatus: false,
      platformId: localStorage.getItem("platformId"),
    };
  },
  computed: {
    infiniteLoadLast() {
      // 我的页面
      if (this.$route.params.pageId == 12 || this.$route.params.pageId == 603) {
        return 0;
      }
      let param = this.lastLayout.param ? JSON.parse(this.lastLayout.param) : {};
      if (this.liveModels.includes(this.lastLayout.subMenu) || this.activityModels.includes(this.lastLayout.subMenu) || this.liveActivityModels.includes(this.lastLayout.subMenu)) {
        // 直播、活动列表相关模块，  是可无限加载的模块
        // 最后一个模块是轮播
        if (param.listStyle && param.listStyle == "carousel") {
          return 0;
        } else {
          return this.infiniteLoad;
        }
      } else {
        return 0;
      }
    },
  },

  watch: {
    configuration: {
      handler(val) {
        if (!val) return;
        this.resetPageState();
        this.processConfigurationData(val);
        this.setupInfiniteLoading();
        this.applyPageStyles();
        this.refreshScrollComponent();
      },
      deep: true,
      immediate: true,
    },
  },

  mounted() {
    this.$nextTick(() => {
      const mainScroll = document.getElementById("mainScroll");
      const setHeight = () => {
        const height = this.isPreviewEnviroment ? "auto" : `${window.innerHeight - 53}px`;
        mainScroll.style.height = height;
      };
      setHeight();
    });
  },
  updated() {},
  beforeDestroy() {
    // 清理防抖定时器
    if (this.refreshScrollTimer) {
      clearTimeout(this.refreshScrollTimer);
      this.refreshScrollTimer = null;
    }
  },
  methods: {
    resetPageState() {
      this.newConfig = [];
      this.page.pageNum = 1;
    },
    processConfigurationData(configuration) {
      try {
        const scrollTabItems = this.extractScrollTabItems(configuration);

        if (scrollTabItems.length > 0) {
          this.handleScrollTabConfiguration(scrollTabItems);
        } else {
          this.handleNormalConfiguration(configuration);
        }
      } catch (error) {
        console.error("处理配置数据时发生错误:", error);
        // 降级处理：使用原始配置
        this.newConfig = [...configuration];
      }
    },
    extractScrollTabItems(configuration) {
      if (!Array.isArray(configuration)) {
        console.warn("配置数据不是数组格式:", configuration);
        return [];
      }
      return configuration.filter((item) => item.menuKey === "");
    },

    handleScrollTabConfiguration(scrollTabItems) {
      try {
        this.scrollNavBar = {
          menuKey: this.UI_CONSTANTS.SCROLL_NAV_BAR_KEY,
          extraInfo: scrollTabItems,
        };

        // 根据当前路由找到对应的组件配置
        const currentTabItem = scrollTabItems.find((item) => item.id == this.$route.params.typeId && item.components);

        if (currentTabItem) {
          this.newConfig = JSON.parse(JSON.stringify(currentTabItem.components));
          // 在指定位置插入滚动导航栏
          this.newConfig.splice(this.UI_CONSTANTS.SCROLL_NAV_INSERT_POSITION, 0, this.scrollNavBar);
        } else {
          console.warn("未找到匹配的滚动标签配置, typeId:", this.$route.params.typeId);
        }
      } catch (error) {
        console.error("处理滚动标签配置时发生错误:", error);
      }
    },
    handleNormalConfiguration(configuration) {
      this.newConfig = [...configuration];
    },
    setupInfiniteLoading() {
      if (!this.allConfig || !this.allConfig.length || !this.newConfig.length) return;

      const currentPageConfig = this.findCurrentPageConfig();
      if (!currentPageConfig) return;

      this.setupLastLayout();
      this.initializeInfiniteLoading();
    },

  
    findCurrentPageConfig() {
      return this.allConfig.find((item) => item.id == this.$route.params.pageId);
    },

    setupLastLayout() {
      const lastConfigItem = this.newConfig[this.newConfig.length - 1];
      const secondLastConfigItem = this.newConfig[this.newConfig.length - 2];

      // 特殊处理：检查是否为特殊布局描述
      const isSpecialLastItem = this.UI_CONSTANTS.SPECIAL_LAYOUT_DESCRIPTIONS.includes(lastConfigItem && lastConfigItem.description);
      this.lastLayout = isSpecialLastItem ? secondLastConfigItem : lastConfigItem;
      this.lastLayoutId = this.lastLayout && this.lastLayout.layoutId;
    },

    initializeInfiniteLoading() {
      if (this.lastLayoutId) {
        this.getHomePageItemList();
      }
      this.getInfiniteLoad();
    },

    applyPageStyles() {
      if (!this.allConfig || !this.allConfig.length) return;

      const currentPageConfig = this.findCurrentPageConfig();
      if (!currentPageConfig) return;

      this.$nextTick(() => {
        this.setScrollContentBackground(currentPageConfig.name);
      });
    },

    setScrollContentBackground(pageName) {
      try {
        const scrollContentElement = document.querySelector(".scroll-content");
        if (!scrollContentElement) {
          return;
        }

        const backgroundStyles = this.getBackgroundStyles(pageName);
        scrollContentElement.style.background = backgroundStyles;
      } catch (error) {
        console.error("设置背景样式时发生错误:", error);
      }
    },

    getBackgroundStyles(pageName) {
      if (pageName === this.UI_CONSTANTS.PAGE_NAMES.MY_PAGE) {
        return this.UI_CONSTANTS.BACKGROUND_STYLES.MY_PAGE;
      }
      return this.UI_CONSTANTS.BACKGROUND_STYLES.DEFAULT;
    },

    /**
     * 刷新滚动组件
     */
    refreshScrollComponent() {
      this.$nextTick(() => {
        this.refreshScroll();
      });
    },

    // 菜单跳转
    toMenuPage(name, components) {
      switch (name) {
        case "hezuo":
          this.cooperationStatus = true;
          break;
        case "quit":
          this.$emit("changeQuitStatus");
          break;
        default:
          location.assign(`${window.location.origin}${window.location.pathname}#/${name}?platformId=${localStorage.getItem("platformId")}&domainType=1`);
      }
    },
    getInfiniteLoad() {
      if (!this.pageMenuList[this.$route.params.pageId]) {
        return;
      }
      getInfiniteLoad({ menu: this.pageMenuList[this.$route.params.pageId] }).then((res) => {
        if (res.code == this.$successCode) {
          this.infiniteLoad = res.data.infiniteLoad;
        } else {
          this.$message.error(res.info);
        }
      });
    },
    // 获取itemIdList 用下拉加载
    getHomePageItemList() {
      let _fun = this.liveActivityModels.includes(this.lastLayout.subMenu)
        ? getCustomLiveActivityRel
        : this.activityModels.includes(this.lastLayout.subMenu)
        ? getHomePageActivityList
        : getHomePageItemList1;
      _fun({ layoutId: this.lastLayoutId }).then((res) => {
        if (res.code == this.$successCode) {
          this.list = res.data;
          this.itemIdList = res.data.map((item) => item.itemId);
        } else {
          this.$message.error(res.info);
        }
      });
    },
    // 上划加载
    selectLoadingItemList() {
      let params = {
        pageNum: this.page.pageNum,
        pageSize: this.page.pageSize,
        layoutId: this.lastLayoutId,
        addedList: this.itemIdList.join(","),
      };
      let _fun = this.liveActivityModels.includes(this.lastLayout.subMenu)
        ? selectLoadingItemActivityList
        : this.activityModels.includes(this.lastLayout.subMenu)
        ? selectLoadingActivityList
        : selectLoadingItemList;
      _fun(params).then((res) => {
        if (res.code == this.$successCode) {
          this.list = this.list.concat(res.data.list);
          let index = this.newConfig.findIndex((res) => {
            return res.id == this.lastLayout.id;
          });
          this.$refs["component" + index][0].updataList(res.data.list);
          // 手机端
          this.page.pageNum++;
          if (!res.data.list.length) {
            this.$refs.scroll.forceUpdate();
          }
        } else {
          this.$message.error(res.info);
        }
      });
    },
    // 自定义图文模块 ，图片 视频 加载完成后 刷新scroll，防止上拉 拉不动
    refreshScroll() {
      if (!this.$refs.scroll) return;
      
      // 添加防抖，避免过于频繁的刷新
      if (this.refreshScrollTimer) {
        clearTimeout(this.refreshScrollTimer);
      }
      
      this.refreshScrollTimer = setTimeout(() => {
        this.$nextTick(() => {
          try {
            // 检查滚动实例是否存在且正常
            if (this.$refs.scroll && this.$refs.scroll.refresh) {
              // 温和的刷新策略，不强制重置位置
              this.$refs.scroll.refresh();
              
              // 确保滚动功能启用，但不重置位置
              if (this.$refs.scroll.enable) {
                this.$refs.scroll.enable();
              }
              
              // 移除强制重置滚动位置的代码，避免用户体验中断
              // this.$refs.scroll.scrollTo(0, 0, 0);
              
              // 延迟刷新减少到更合理的时间
              setTimeout(() => {
                if (this.$refs.scroll && this.$refs.scroll.refresh) {
                  this.$refs.scroll.refresh();
                }
              }, 100); // 从300ms减少到100ms
            }
          } catch (error) {
            console.warn('刷新滚动时出现错误:', error);
          }
        });
      }, 50); // 50ms防抖
    },
  },
};
</script>
<style lang="scss" scoped>
.cube-slide {
  height: auto;
}
.scroll-content {
  overflow: hidden;
  position: relative;
  width: 100%;
  //background: linear-gradient(180deg, #ffffff 0%, #f2f1f6 100%);
  box-shadow: 0px 4px 10px 0px rgba(0, 0, 0, 0.05);
  &.scroll-content-car {
    // background: linear-gradient(180deg, #f4f6fc 0%, #f4f6fc 100%);
    :deep .header_input {
      background-color: #fff;
    }
    :deep .type-group {
      background-color: #fff;
      margin: 12px 15px;
      border-radius: 4px;
      & > div {
        margin-bottom: 13px;
      }
    }
    :deep .recommend {
      background: none;
    }
    :deep .dots .active {
      background: #5a2e84;
    }
    :deep .dots > span {
      background: rgba(90, 46, 132, 0.2);
    }
    :deep .left-content-time .icon {
      background: #5a2e84;
    }
    :deep .classify-list-container {
      background: none;
    }
  }
}
.PreviewEnviroment {
  position: absolute;
  width: 100%;
  height: 100%;
  background-color: #ff000000;
  top: 0;
}
// #mainScroll{
//     padding-bottom: 50px;
// }
</style>
<style lang="scss">
.popShow {
  .cube-scroll-content {
    transform: none !important;
  }
}
</style>
