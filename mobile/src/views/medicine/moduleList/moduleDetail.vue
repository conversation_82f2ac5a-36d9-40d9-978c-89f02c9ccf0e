<!-- 视频详情 -->
<template>
  <div class="video-detail-wrap">
    <div class="video-detail-header">{{ videoInfo.title }}</div>
    <div class="video-detail-desc">{{ videoInfo.oneSentenceIndex }}</div>
    <div class="video-detail-time">{{ formatTime(videoInfo.publishTime) }}</div>
    <div class="video-detail-content" v-if="videoInfo.content && videoInfo.content.length">
      <div class="video-detail-content-list-text" v-for="(item, index) in videoInfo.content" :key="index">
        <div class="video-detail-content-list-text-item video-container" v-if="item.type == 'video'" :style="{ 'padding-bottom': videoAspectRatio + '%' }">
          <!-- 视频播放的按钮 -->
          <div class="video-detail-content-play flex-center flex-middle" v-if="!videoStatus" @click="playVideo">
            <img src="https://oss.ienmore.com/frontUpload/partUpload/2025-06-03/71cf7a0aba3027e975850a642723a6c4.png" alt="播放按钮" />
          </div>
          <!-- 视频封面 -->
          <div class="video-detail-content-cover" v-if="!videoStatus">
            <img ref="coverImg" :src="videoInfo.coverUrl" alt="视频封面" @load="onCoverLoad" />
          </div>
          <!-- 视频播放 -->
          <video v-if="videoStatus" ref="videoPlayer" class="video-detail-content-video" :src="item.url" controls @loadedmetadata="onVideoLoad"></video>
        </div>
        <div class="video-detail-content-list-text-item text-container" v-else-if="item.type == 'text'">
          <div class="video-detail-content-list-text-item-text" v-html="item.text"></div>
        </div>
        <div class="video-detail-content-list-text-item image-container" v-else-if="item.type == 'img'">
          <img :src="item.url" alt="图片" />
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import { getShortVideoDetail } from "@/api/medicine/homePage";
import { getInformationDetail } from "@/api/information";
export default {
  name: "videoDetail",
  data() {
    return {
      // 视频播放状态
      videoStatus: false,
      // 视频是否已经开始播放过
      hasPlayed: false,
      // 视频的信息
      videoInfo: {},
      // 视频容器的宽高比 (height/width * 100)
      videoAspectRatio: 56.25, // 默认16:9比例
      // 是否是iOS微信浏览器
      isIOSWeChat: false,
      // 是否已经设置了正确的比例
      hasCorrectRatio: false,
    };
  },
  mounted() {
    this.detectIOSWeChat();
    this.getModuleDetail();
  },
  methods: {
    // 检测是否是iOS微信浏览器
    detectIOSWeChat() {
      const ua = navigator.userAgent.toLowerCase();
      this.isIOSWeChat = /iphone|ipad|ipod/.test(ua) && /micromessenger/.test(ua);
      console.log('是否是iOS微信浏览器:', this.isIOSWeChat);
    },
    // 获取模块详情
    async getModuleDetail() {
      const modelType = this.$route.params.modelType;
      
      // 获取对应的API方法
      const getApiMethod = (type) => {
        const apiMap = {
          video: getShortVideoDetail,
          news: getInformationDetail,
        };
        return apiMap[type] || apiMap.default;
      };
      
      const apiMethod = getApiMethod(modelType);
      const res = await apiMethod({ id: this.$route.params.id, source: 'live' });
      
      if (res.code == this.$successCode) {
        this.videoInfo = res.data || {};
        
        // 处理内容数据
        const processContent = () => {
          if (!this.videoInfo.content) return;
          
          const content = JSON.parse(this.videoInfo.content) || [];
          this.$set(this.videoInfo, "content", content);
          
          // 为视频类型添加额外的视频内容
          if (modelType === 'video') {
            addVideoContentItem();
          }
        };
        
        // 添加视频内容项
        const addVideoContentItem = () => {
          const videoItem = {
            type: 'video',
            url: this.videoInfo.commonFile.fileConvertUrl,
            coverUrl: this.videoInfo.fileCover,
          };
          this.videoInfo.content.unshift(videoItem);
        };
        
        // 将方法绑定到实例以便在其他地方使用
        this.addVideoContentItem = addVideoContentItem;
        
        processContent();
        
        // 预加载视频元数据以获取正确的比例
        this.preloadVideoMetadata();
      } else {
        this.$toast(res.info);
      }
    },
    
    // 预加载视频元数据
    preloadVideoMetadata() {
      const videoItem = this.videoInfo.content.find(item => item.type === 'video');
      if (videoItem && videoItem.url) {
        // iOS微信浏览器使用不同的策略
        if (this.isIOSWeChat) {
          this.handleIOSWeChatVideoRatio(videoItem);
        } else {
          // 普通浏览器的处理逻辑
          const tempVideo = document.createElement('video');
          tempVideo.src = videoItem.url;
          tempVideo.preload = 'metadata';
          tempVideo.addEventListener('loadedmetadata', () => {
            if (tempVideo.videoWidth && tempVideo.videoHeight) {
              const aspectRatio = (tempVideo.videoHeight / tempVideo.videoWidth) * 100;
              this.setVideoAspectRatio(aspectRatio, '预加载视频');
            }
            tempVideo.remove();
          });
          
          // 设置超时备用方案
          setTimeout(() => {
            if (!this.hasCorrectRatio) {
              this.useCoverImageRatio();
            }
          }, 1500);
        }
      }
    },
    
    // iOS微信浏览器的特殊处理
    handleIOSWeChatVideoRatio(videoItem) {
      // 方法1：通过封面图片获取比例
      this.useCoverImageRatio();
      
      // 方法2：创建隐藏的video元素但不预加载
      const tempVideo = document.createElement('video');
      tempVideo.style.position = 'absolute';
      tempVideo.style.top = '-9999px';
      tempVideo.style.left = '-9999px';
      tempVideo.style.width = '1px';
      tempVideo.style.height = '1px';
      tempVideo.style.opacity = '0';
      tempVideo.src = videoItem.url;
      tempVideo.muted = true;
      tempVideo.playsInline = true;
      
      document.body.appendChild(tempVideo);
      
      // 监听视频加载
      const handleLoadedMetadata = () => {
        if (tempVideo.videoWidth && tempVideo.videoHeight) {
          const aspectRatio = (tempVideo.videoHeight / tempVideo.videoWidth) * 100;
          this.setVideoAspectRatio(aspectRatio, 'iOS微信浏览器视频元数据');
        }
        tempVideo.removeEventListener('loadedmetadata', handleLoadedMetadata);
        document.body.removeChild(tempVideo);
      };
      
      tempVideo.addEventListener('loadedmetadata', handleLoadedMetadata);
      
      // 尝试加载一小段视频来获取元数据
      tempVideo.currentTime = 0.1;
      
      // 清理超时
      setTimeout(() => {
        if (document.body.contains(tempVideo)) {
          tempVideo.removeEventListener('loadedmetadata', handleLoadedMetadata);
          document.body.removeChild(tempVideo);
        }
      }, 3000);
    },
    
    // 使用封面图片比例
    useCoverImageRatio() {
      this.$nextTick(() => {
        const img = this.$refs.coverImg;
        if (img) {
          if (img.complete && img.naturalWidth && img.naturalHeight) {
            const aspectRatio = (img.naturalHeight / img.naturalWidth) * 100;
            this.setVideoAspectRatio(aspectRatio, '封面图片');
          } else {
            img.onload = () => {
              if (img.naturalWidth && img.naturalHeight) {
                const aspectRatio = (img.naturalHeight / img.naturalWidth) * 100;
                this.setVideoAspectRatio(aspectRatio, '封面图片加载完成');
              }
            };
          }
        }
      });
    },
    
    // 设置视频宽高比
    setVideoAspectRatio(aspectRatio, source) {
      if (!this.hasCorrectRatio || this.isIOSWeChat) {
        this.videoAspectRatio = aspectRatio;
        this.hasCorrectRatio = true;
        console.log(`通过${source}设置比例:`, aspectRatio);
        
        // 强制重新渲染
        this.$forceUpdate();
        
        // iOS微信浏览器需要额外的强制刷新
        if (this.isIOSWeChat) {
          this.$nextTick(() => {
            // 触发重排
            const container = document.querySelector('.video-container');
            if (container) {
              container.style.display = 'none';
              container.offsetHeight; // 触发重排
              container.style.display = '';
            }
          });
        }
      }
    },
    
    // 播放视频
    playVideo() {
      this.videoStatus = true;
      this.hasPlayed = false;
    },
    
    // 封面图片加载完成
    onCoverLoad() {
      const img = this.$refs.coverImg;
      if (img && img.naturalWidth && img.naturalHeight) {
        console.log('封面图片加载完成，比例:', img.naturalWidth / img.naturalHeight);
        
        // iOS微信浏览器立即使用封面比例
        if (this.isIOSWeChat) {
          const aspectRatio = (img.naturalHeight / img.naturalWidth) * 100;
          this.setVideoAspectRatio(aspectRatio, '封面图片(iOS微信)');
        } else {
          // 普通浏览器的备用逻辑
          setTimeout(() => {
            if (!this.hasCorrectRatio) {
              const aspectRatio = (img.naturalHeight / img.naturalWidth) * 100;
              this.setVideoAspectRatio(aspectRatio, '封面图片(备用)');
            }
          }, 1000);
        }
      }
    },

    // 视频元数据加载完成
    onVideoLoad(event) {
      const video = event ? event.target : this.$refs.videoPlayer;
      if (video && video.videoWidth && video.videoHeight) {
        console.log('视频播放器加载完成，比例:', video.videoWidth / video.videoHeight);
        
        // 最终确认比例设置
        const aspectRatio = (video.videoHeight / video.videoWidth) * 100;
        if (!this.hasCorrectRatio || Math.abs(this.videoAspectRatio - aspectRatio) > 1) {
          this.setVideoAspectRatio(aspectRatio, '视频播放器');
        }

        // 自动播放视频
        if (this.videoStatus && !this.hasPlayed && typeof video.play === "function") {
          this.hasPlayed = true;
          video.play().catch((error) => {
            console.log("视频自动播放失败:", error);
          });
        }
      }
    },
  },
};
</script>
<style scoped lang="scss">
.video-detail-wrap {
  width: 100%;
  padding: 15px;
  box-sizing: border-box;
  .video-detail-header {
    font-weight: 500;
    font-size: 14px;
    color: #333330;
    line-height: 20px;
    margin-bottom: 10px;
  }
  .video-detail-desc {
    font-weight: 400;
    font-size: 12px;
    color: #666666;
    line-height: 16px;
    margin-bottom: 6px;
  }
  .video-detail-time {
    font-weight: 400;
    font-size: 12px;
    color: #666666;
    line-height: 16px;
    margin-bottom: 15px;
  }
  .video-detail-content {
    width: 100%;
    
    .video-detail-content-list-text {
      width: 100%;
      
      .video-detail-content-list-text-item {
        margin-bottom: 15px;
        
        &.video-container {
          position: relative;
          width: 100%;
          height: 0;
          border-radius: 8px;
          overflow: hidden;
          background-color: #f5f5f5;
          margin-bottom: 20px;
          // iOS微信浏览器的优化
          -webkit-transform: translateZ(0);
          transform: translateZ(0);
          -webkit-backface-visibility: hidden;
          backface-visibility: hidden;
        }
        
        &.text-container {
          .video-detail-content-list-text-item-text {
            font-size: 14px;
            line-height: 1.6;
            color: #333;
            word-wrap: break-word;
          }
        }
        
        &.image-container {
          img {
            width: 100%;
            height: auto;
            border-radius: 6px;
            display: block;
          }
        }
      }
    }
  
    .video-detail-content-play {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
      z-index: 3;
      cursor: pointer;
      transition: opacity 0.3s ease;
      background-color: rgba(0, 0, 0, 0.1);

      &:hover {
        opacity: 0.8;
      }

      img {
        width: 44px;
        height: 44px;
        filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3));
      }
    }

    .video-detail-content-cover {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      z-index: 2;
      // iOS微信浏览器的优化
      -webkit-transform: translateZ(0);
      transform: translateZ(0);

      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        object-position: center;
        display: block;
        // iOS微信浏览器图片渲染优化
        -webkit-backface-visibility: hidden;
        backface-visibility: hidden;
        image-rendering: -webkit-optimize-contrast;
        image-rendering: optimize-contrast;
      }
    }

    .video-detail-content-video {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      object-fit: contain;
      object-position: center;
      outline: none;
      z-index: 2;
    }
  }
}
</style>
