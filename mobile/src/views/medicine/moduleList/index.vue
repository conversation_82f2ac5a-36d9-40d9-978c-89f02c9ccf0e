<!-- 共 -->
<template>
  <div class="medicineSearch flex-vertical">
    <!-- 搜索 -->
    <div class="unit-0">
      <div class="searchHeadWrap flex-left unit-0">
        <div class="searchLeft unit flex-middle">
          <div class="center_search">
            <img src="@/assets/img/search.png" alt="" />
            <iframe id="rfFrame" name="rfFrame" src="about:blank" style="display: none"></iframe>
            <form action="about:blank" class="form_search" @submit.prevent="search" target="rfFrame">
              <div class="form-search-div">
                <input type="search" v-model="searchOptions.keywords" autofocus="autofocus" placeholder="请输入关键字" id="searchInput" />
                <div v-show="searchOptions.keywords" class="clear-button" @click="clearData">
                  <svg width="8px" height="8px" viewBox="0 0 15 15" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
                    <g id="页面-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
                      <g id="画板备份-7" transform="translate(-572.000000, -176.000000)" fill-rule="nonzero">
                        <g id="删除" transform="translate(570.000000, 174.000000)">
                          <rect id="矩形" fill="#000000" opacity="0" x="0" y="0" width="20" height="19"></rect>
                          <polygon
                            id="路径"
                            fill="#999999"
                            points="17.8947368 15.9285714 11.1278196 9.5 17.8947368 3.07142858 16.7669173 2 10 8.42857142 3.23308272 2 2.10526316 3.07142858 8.87218044 9.5 2.10526316 15.9285714 3.23308272 17 10 10.5714286 16.7669173 17"
                          ></polygon>
                        </g>
                      </g>
                    </g>
                  </svg>
                </div>
              </div>
            </form>
          </div>
        </div>
        <div class="searchRight unit-0 flex-center flex-middle">
          <div class="list-sort flex-middle">
            <div class="list-sort-btn" :class="{ active: selectType }" @click="triggerDropMenu">
              <span>排序</span>
              <img v-if="!selectType" class="sort-icon" src="https://oss.ienmore.com/frontUpload/partUpload/2025-06-03/656b774ee99e2f237eb6cc917e1a75a6.png" alt="" />
              <img v-else class="sort-icon" src="https://oss.ienmore.com/frontUpload/partUpload/2025-06-03/715b23b934918e0a8b6eafba7dce2f70.png" alt="" />
            </div>
            <cube-tip ref="dropMenu" direction="top" offsetRight="17px" class="list-drop-menu">
              <div v-for="(item, index) in sortList" :key="index" @click="changeType(item.value)" :class="{ active: selectType == item.value }">{{ item.label }}</div>
            </cube-tip>
          </div>
        </div>
      </div>
    </div>
    <!-- 查询结果 -->
    <div class="unit content" :style="{ backgroundColor: $route.params.modelType == 'video' ? '#fff' : '#f5f5f5' }">
      <cube-scroll v-if="moduleList && moduleList.length" class="item-list" ref="classifyScroll" :data="moduleList" :options="options" @pulling-down="onPullingDown" @pulling-up="onPullingUp">
        <div class="video-list flex-wrap flex-middle">
            <common-video v-if="$route.params.modelType == 'video'" v-for="(item, index) in moduleList" :key="index" :item="item"></common-video>
            <common-information v-if="$route.params.modelType == 'news'" v-for="(item, index) in moduleList" :key="index" :item="item"></common-information>
        </div>
      </cube-scroll>
      <div class="noData flex-middle flex-center" v-else>{{ isLoaded ? "暂无更多数据" : "数据加载中..." }}</div>
    </div>
  </div>
</template>

<script>
import CommonVideo from "@/components/common/commonVideo.vue";
import { getShortVideoList } from "@/api/shortVideo";
import { getInformationList } from "@/api/information";
import CommonInformation from "@/components/common/commonInformation";
export default {
  name: "ModuleList",
  data() {
    return {
      moduleList: [],
      options: {
        pullUpLoad: {
          threshold: 50,
          txt: {
            more: "",
            noMore: "没有更多信息啦！",
          },
        },
        pullDownRefresh: {
          threshold: 45,
          stopTime: 1000,
          txt: "更新成功",
        },
        scrollbar: false,
      },
      pageData: {
        pageNum: 1,
        pageSize: 10,
      },
      total: 0,
      searchOptions: {
        keywords: "",
        status: 1,
        source: "live",
        order: "",
      },
      sortList: [
        { value: "", label: "默认排序" },
        { value: "publishTime", label: this.$route.params.modelType == "video" ? "按上传时间排序" : "按发布时间排序" },
        { value: "visitorsNumber", label: this.$route.params.modelType == "video" ? "按播放量排序" : "按浏览人数排序" },
      ],
      selectType: "",
      // 控制排序下拉菜单的显示
      dropMenuShow: false,
      // 是否已加载过数据
      isLoaded: false,
    };
  },

  created() {
    this.getModuleList();
  },

  components: {
    CommonVideo,
    CommonInformation,
  },

  computed: {},

  mounted() {},

  methods: {
    async getModuleList() {
      this.isLoaded = false;
      const params = Object.assign({}, this.pageData, this.searchOptions);
      const fn = this.$route.params.modelType == "video" ? getShortVideoList : getInformationList;
      const res = await fn(params);
      this.isLoaded = true;
      if (res.code == this.$successCode) {
        this.moduleList = this.moduleList.concat(res.data.list);
        this.total = res.data.total;
      } else {
        this.$toast(res.info);
      }
    },
    async onPullingDown() {
      this.pageData.pageNum = 1;
      this.moduleList = [];
      await this.getModuleList();
      this.$nextTick(() => {
        if (this.$refs.classifyScroll) {
          this.$refs.classifyScroll.forceUpdate();
        }
      });
    },
    async onPullingUp() {
      if (this.hasMoreData()) {
        await this.loadMoreData();
      } else {
        this.finishPullUp();
      }
    },

    // 检查是否还有更多数据
    hasMoreData() {
      return this.moduleList.length < this.total;
    },

    // 加载更多数据
    async loadMoreData() {
      try {
        this.pageData.pageNum++;
        await this.getModuleList();
        this.$nextTick(() => {
          this.refreshScrollComponent();
        });
      } catch (error) {
        console.error('加载更多数据失败:', error);
      }
    },

    // 完成上拉操作
    finishPullUp() {
      this.$nextTick(() => {
        this.forceUpdateScrollComponent();
      });
    },

    // 刷新滚动组件
    refreshScrollComponent() {
      if (this.$refs.classifyScroll) {
        this.$refs.classifyScroll.refresh();
      }
    },

    // 强制更新滚动组件
    forceUpdateScrollComponent() {
      if (this.$refs.classifyScroll) {
        this.$refs.classifyScroll.forceUpdate();
      }
    },

    search() {
      this.moduleList = [];
      this.pageData.pageNum = 1;
      this.getModuleList();
    },
    clearData() {
      this.searchOptions.keywords = "";
      this.search();
    },
    changeType(val) {
      this.moduleList = [];
      this.pageData.pageNum = 1;
      this.selectType = val;
      this.searchOptions.order = val;
      this.dropMenuShow = !this.dropMenuShow;
      this.getModuleList();
    },
    triggerDropMenu() {
      this.dropMenuShow ? this.$refs.dropMenu.hide() : this.$refs.dropMenu.show();
      this.dropMenuShow = !this.dropMenuShow;
    },
  },
};
</script>
<style scoped lang="scss">
.medicineSearch {
  position: relative;
  .content {
    overflow-y: scroll;
    padding: 10px 15px;
  }
}
.noData {
  font-size: 14px;
  color: #999;
  height: 100%;
}

.searchHeadWrap {
  margin-top: 10px;
  margin-bottom: 5px;
  width: 100%;
  padding: 0 15px;
  background-color: #fff;
}
.iconArrow {
  width: 9.5px;
  height: 17px;
  background: url("../../../assets/img/leftArrow.png") no-repeat;
  background-size: 100%;
}

.searchRight {
  box-sizing: border-box;
}

.center_search {
  height: 36px;
  width: 100%;
  display: flex;
  align-items: center;
  background: #f1f2f3;
  border-radius: 4px;
}

.center_search img {
  width: 12px;
  height: 12px;
  margin-right: 9px;
  vertical-align: middle;
  margin-left: 13px;
}

#searchInput {
  outline: none;
  background: transparent;
  border: none;
  font-size: 14px;
  line-height: 22px;
  width: 90%;
  padding-left: 2px;
}

input::-webkit-search-cancel-button {
  display: none;
}

input[type="search"]::-ms-clear {
  display: none;
}

#searchInput::placeholder {
  font-size: 16px;
  line-height: 18px;
  color: #999999;
  transform: translateY(2px);
}

.form_search {
  width: 100%;
}

.form-search-div {
  width: 100%;
  display: flex;
  align-items: center;
  height: 22px;
}

.search-button {
  margin-left: 8px;
  height: 35px;
  width: 35px;
  line-height: 35px;
  text-align: right;
  font-size: 14px;
  font-weight: 400;
  color: rgba(153, 153, 153, 1);
}
</style>
<style lang="scss">
.list-sort {
  position: relative;
  .list-sort-btn {
    margin-left: 10px;
    font-weight: 400;
    font-size: 14px;
    color: #666666;
    .sort-icon {
      width: 14px;
      height: 14px;
      vertical-align: middle;
      margin-left: 4px;
    }
    &.active {
      color: var(--color-primary);
    }
  }
  .list-drop-menu {
    width: 114px;
    position: absolute;
    top: 33px;
    right: 0;
    background: #fff;
    max-height: none;
    border-radius: 4px;
    padding: 0;
    box-shadow: 0px 9px 28px 8px rgba(0, 0, 0, 0.05), 0px 6px 16px 0px rgba(0, 0, 0, 0.08), 0px 3px 6px -4px rgba(0, 0, 0, 0.12);
    .cube-tip-close {
      display: none;
    }
    .cube-tip-angle {
      top: 1px;
      right: 12px !important;
      &::before {
        border-width: 0;
        width: 9px;
        height: 9px;
        background: #fff;
        transform: rotate(45deg);
        box-shadow: 0 -8px 10px 0px rgba(0, 0, 0, 0.1);
      }
    }
    .cube-tip-content {
      line-height: 36px !important;
      color: #999;
      font-size: 12px;
      z-index: 1;
      white-space: nowrap;
      & > div {
        padding: 0 15px;
        &.active {
          color: var(--color-primary);
        }
      }
    }
  }
}
</style>
