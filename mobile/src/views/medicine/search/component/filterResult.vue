<!-- 查询结果 -->
<template>
    <filter-dialog>
        <div class="filter-result flex-vertical">
            <div class="unit-0 fliter-content">
                <div class='searchHeadWrap  flex-left unit-0'>
                    <div class="searchLeft unit flex-middle">
                        <div class="center_search">
                            <img src="@/assets/img/search.png" alt="">
                            <iframe id="rfFrame" name="rfFrame" src="about:blank" style="display:none;"></iframe>
                            <form action="about:blank" class="form_search" @submit.prevent="search" target="rfFrame">
                                <div class="form-search-div">
                                    <input type="search" v-model="contentList.name" autofocus="autofocus" placeholder="请输入关键字" id="searchInput" />
                                    <div v-show="contentList.name" class="clear-button" @click="clearData">
                                        <svg width="8px" height="8px" viewBox="0 0 15 15" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
                                            <g id="页面-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
                                                <g id="画板备份-7" transform="translate(-572.000000, -176.000000)" fill-rule="nonzero">
                                                    <g id="删除" transform="translate(570.000000, 174.000000)">
                                                        <rect id="矩形" fill="#000000" opacity="0" x="0" y="0" width="20" height="19"></rect>
                                                        <polygon id="路径" fill="#999999" points="17.8947368 15.9285714 11.1278196 9.5 17.8947368 3.07142858 16.7669173 2 10 8.42857142 3.23308272 2 2.10526316 3.07142858 8.87218044 9.5 2.10526316 15.9285714 3.23308272 17 10 10.5714286 16.7669173 17"></polygon>
                                                    </g>
                                                </g>
                                            </g>
                                        </svg>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                    <div class="searchRight unit-0 flex-center flex-middle">
                        <!-- <div v-show="true" @click="searchFun" class="search-button">搜索</div> -->
                        <!-- <div @click="goHome" class="search-button">取消</div> -->
                      <div class="list-sort">
                        <div class="list-sort-btn" @click="triggerDropMenu">
                          筛选
                        <i class="cubeic-select"></i>
                        </div>
                        <cube-tip ref="dropMenu" direction="top" offsetRight="17px" class="list-drop-menu">
                        <div v-for="(item, index) in typeList" :key="index" @click="changeType(item.value)" :class="{active: selectType==item.value}">{{item.label}}</div>
                        </cube-tip>
                </div>
                    </div>
                    <cube-popup type="my-popup" :mask="false" content="<i style='padding:20px;background:rgba(0,0,0,.8);color:#fff'>搜索内容不能为空</i>" ref="myPopup" />
                </div>
                <filter-classify :filterName="filterName" :filterData="filterData" @openDialog="changeTagData"></filter-classify>
                <div class="fliter-tag flex-left flex-wrap">
                    <div :class="['tag',{'active-tag':item.isSelect && (filterName=='classify'||filterName=='order'),'active-channel':item.isSelect && filterName=='channel','active-type':item.isSelect && filterName=='type'}]" 
                    v-for="item,index in tagData" :key="index" @click="clickSelect(index,tagData,item)">
                        {{item.codeValueDesc}}
                    </div>
                </div>
                <div class="bottomGroup flex-left">
                    <div class="reset unit flex-middle flex-center" @click="reset">重置</div>
                    <div class="sure unit flex-middle flex-center" @click="sure">确定</div>
                </div>
            </div>
            <div class="unit"></div>
        </div>
    </filter-dialog>
</template>

<script>
import FilterDialog from './searchDialog';
import FilterClassify from './filterClassify';
import { getClassify,getIndustryClassify } from '@/api/medicine/homePage';
export default {
    name: '',
    props: ['filterType', 'filterData', 'selectOption', 'searchName','selectTypeNew'],
    data () {
        return {
            tagData: [],
            filterName: '',
            contentList: {
                channelList: [],
                classifyList: [],
								typeList: [],
                order: '',
                name: '',
            },
            channelList: [],
            classifyList: [],
						liveTypeList: [],
            orderList: [
                { codeValueId: 'beginTime', codeValueDesc: '按时间排序', isSelect: true },
                { codeValueId: 'clickRate', codeValueDesc: '按热度排序', isSelect: false }
            ],
            dropMenuShow:false,
              typeList:[
                {value:'all',label:'全部'},
                {value:1,label: '仅看收费直播'},
                {value:0,label: '仅看免费直播'},
                
             ],
            selectType:'all'
        };
    },

    created () {
        this.selectType=this.selectTypeNew
        let temArr = [];
        if(this.$route.query.platformId == 5){
            temArr =['root_system_info_car_live_channel_publish','root_system_info_car_live_classify_publish','root_system_info_car_live_type_publish'];;
        }else{
            temArr = ['root_system_info_medical_live_channel_publish','root_system_info_medical_live_classify_publish','root_system_info_medical_live_type_publish'];
        }
        // 进行数据的筛选
        // 组合行业的接口参数
        const industryParams = {
            layoutId:1051,
            parentId:temArr[0]
        }
        Promise.all([getIndustryClassify(industryParams), getClassify(temArr[1]), getClassify(temArr[2])]).then((res) => {
            this.channelList = res[0].data.map((item) => {
                return {
                    ...item,
                    isSelect: false
                }
            })
            this.classifyList = res[1].data.map((item) => {
                return {
                    ...item,
                    isSelect: false
                }
            })
            this.liveTypeList = res[2].data.map((item) => {
                return {
                    ...item,
                    isSelect: false
                }
            })
            // 处理行业参数（支持 channel 和 channal 两种拼写）
            if (this.selectOption.hasOwnProperty('channel') || this.selectOption.hasOwnProperty('channal')) {
                const channelValue = this.selectOption.channel || this.selectOption.channal;
                this.channelList.forEach((item) => {
                    if (item.codeValueId == channelValue) {
                        item.isSelect = true;
                        this.contentList.channelList.push(item.codeValueId);
                    }
                })
            }
            
            // 处理职能参数
            if (this.selectOption.hasOwnProperty('classify')) {
                this.classifyList.forEach((item) => {
                    if (item.codeValueId == this.selectOption.classify) {
                        item.isSelect = true;
                        this.contentList.classifyList.push(item.codeValueId);
                    }
                })
            }
            
            // 处理类型参数
            if (this.selectOption.hasOwnProperty('type')) {
                this.liveTypeList.forEach((item) => {
                    if (item.codeValueId == this.selectOption.type) {
                        item.isSelect = true;
                        this.contentList.typeList.push(item.codeValueId);
                    }
                })
            }
        })
    },

    components: {
        FilterDialog,
        FilterClassify
    },

    watch: {
        filterType () {
            this.filterName = this.filterType;
            this.changeTagData(this.filterName);
            this.contentList.name = this.searchName;
        }
    },

    computed: {},

    mounted () { },

    methods: {
        search () {
            this.$emit('closeDialog', this.contentList);
        },
        clearData () {
            this.contentList.name = '';
        },
        goHome () {
            this.$emit('goHome');
        },
        closeDialog () { 
            this.$emit('closeDialog', this.contentList);
        },
        // 根据类型取值
        changeTagData (type) {
            this.filterName = type;
            switch (type) {
                case 'channel':
                    this.tagData = this.channelList;
                    break;
                case 'classify':
                    this.tagData = this.classifyList;
                    break;
                case 'type':
                    this.tagData = this.liveTypeList;
                    break;
                case 'order':
                    this.tagData = this.orderList;
                    break;
            }
        },
        clickSelect (index, data, item) {
            if (this.filterName == 'order') {
                this.tagData.forEach((items) => {
                    if (items.codeValueId == item.codeValueId) {
                        items.isSelect = true;
                    } else {
                        items.isSelect = false;
                    }
                })
            } else {
                this.$set(data[index], 'isSelect', !data[index]['isSelect']);
            }
            if (data[index]['isSelect']) {
                switch (this.filterName) {
                    case 'channel':
                        this.contentList.channelList.push(item.codeValueId);
                        break;
                    case 'classify':
                        this.contentList.classifyList.push(item.codeValueId);
                        break;
                    case 'order':
                        this.contentList.order = item.codeValueId;
                        break;
                    case 'type':
                        this.contentList.typeList.push(item.codeValueId);
                        break;
                }
            } else {
                switch (this.filterName) {
                    case 'channel':
                        this.contentList.channelList = this.contentList.channelList.filter((subItme) => {
                            return subItme != item.codeValueId;
                        })
                        break;
                    case 'classify':
                        this.contentList.classifyList = this.contentList.classifyList.filter((subItme) => {
                            return subItme != item.codeValueId;
                        })
                        break;
                    case 'type':
                        this.contentList.typeList = this.contentList.typeList.filter((subItme) => {
                            return subItme != item.codeValueId;
                        })
                        break;
                }
            }
        },
        reset () {
            this.$emit('reset');
            this.resetData();
        },
        resetData () { 
            let zhanshi = this.filterName
            switch (zhanshi) {
                case 'channel':
                    this.channelList = this.channelList.map((item) => {
                    return {
                        ...item,
                        isSelect: false
                    }
                })
                break;
                case 'classify':
                    this.classifyList = this.classifyList.map((item) => {
                    return {
                        ...item,
                        isSelect: false
                    }
                })
                break;
                case 'type':
                    this.typeList = this.classifyList.map((item) => {
                    return {
                        ...item,
                        isSelect: false
                    }
                })
                break;
                case 'order':
                    this.orderList = [
                        { codeValueId: 'beginTime', codeValueDesc: '按时间排序', isSelect: true },
                        { codeValueId: 'clickRate', codeValueDesc: '按热度排序', isSelect: false }
                    ]
                break;
            }
            // this.channelList = this.channelList.map((item) => {
            //     return {
            //         ...item,
            //         isSelect: false
            //     }
            // })
            // this.classifyList = this.classifyList.map((item) => {
            //     return {
            //         ...item,
            //         isSelect: false
            //     }
            // })
            // this.orderList = [
            //     { codeValueId: 'beginTime', codeValueDesc: '按时间排序', isSelect: true },
            //     { codeValueId: 'clickRate', codeValueDesc: '按热度排序', isSelect: false }
            // ]
            this.tagData.forEach((item)=>{
                item.isSelect = false;
            })
        },
        sure () {
            this.closeDialog();
        },
     changeType(val){
         this.selectType=val
        //  this.search()
        this.$emit('changeTypeNew',val)
         this.dropMenuShow = !this.dropMenuShow;
        },
    triggerDropMenu() {
      if (this.dropMenuShow) {
        this.$refs.dropMenu.hide()
      } else {
        this.selectType=this.selectTypeNew
        this.$refs.dropMenu.show()
      }
      this.dropMenuShow = !this.dropMenuShow;
    },
    }
}

</script>
<style  scoped lang="scss">
.filter-result {
    height: 100%;
    .fliter-content {
        background: #fff;
        .fliter-tag {
            width: 100%;
            box-sizing: border-box;
            // padding: 5px 20px 20px;
            padding: 5px 0px 20px 20px;
            background: #fff;
            .tag {
                // padding: 0px 10px;
                // height: 28px;
                // background: rgba(247, 247, 247, 0.5);
                border-radius: 2px;
                line-height: 32px;
                font-size: 13px;
                font-family: PingFangSC-Regular, PingFang SC;
                font-weight: 400;
                color: #666666;
                margin-right: 10px;
                margin-top: 10px;
                width: 105px;
                height: 32px;
                background: #F5F6FA;
                text-align: center;
            }
            .active-tag {
                background: rgba(20, 100, 161, 0.1);
                color: var(--color-primary);
            }
            .active-channel {
                background: rgba(235, 123, 33, 0.1);
                color: #eb7b21;
            }
						.active-type{
							background: rgba(229, 243, 242, 1);
							color: #175F5F;
						}
        }
        .bottomGroup {
            width: 100%;
            height: 40px;
            font-size: 14px;
            font-family: PingFangSC-Regular, PingFang SC;
            font-weight: 400;
            .reset {
                background: #e7eff5;
                color: #1464a1;
            }
            .sure {
                background: #1464a1;
                color: #ffffff;
            }
        }
    }
}
.searchHeadWrap {
    margin-top: 10px;
    margin-bottom: 5px;
    width: 100%;
    padding: 0 15px;
    background-color: #fff;
}
.searchLeft {
}
.iconArrow {
    width: 9.5px;
    height: 17px;
    background: url("../../../../assets/img/leftArrow.png") no-repeat;
    background-size: 100%;
}
.searchRight {
    box-sizing: border-box;
}
.center_search {
    height: 36px;
    width: 100%;
    display: flex;
    align-items: center;
    background: #f1f2f3;
    border-radius: 4px;
}
.center_search img {
    width: 12px;
    height: 12px;
    margin-right: 9px;
    vertical-align: middle;
    margin-left: 13px;
}
#searchInput {
    outline: none;
    background: transparent;
    border: none;
    font-size: 14px;
    line-height: 22px;
    width: 90%;
    padding-left: 2px;
}
input::-webkit-search-cancel-button {
    display: none;
}
input[type="search"]::-ms-clear {
    display: none;
}

#searchInput::placeholder {
    font-size: 16px;
    line-height: 18px;
    color: #999999;
    transform: translateY(2px);
}
.form_search {
    width: 100%;
}
.form-search-div {
    width: 100%;
    display: flex;
    align-items: center;
    height: 22px;
}
.search-button {
    margin-left: 8px;
    height: 35px;
    width: 35px;
    line-height: 35px;
    text-align: right;
    font-size: 14px;
    font-weight: 400;
    color: rgba(153, 153, 153, 1);
}
</style>