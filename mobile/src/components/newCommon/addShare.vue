<template>
  <div>
      <div 
        class="addXiaoyi" 
        v-if="(sliderInfoStatus.addxiaoyi || sliderInfoStatus.addweixin) && isShowIcon"
        :style="{ left: position.x + 'px', top: position.y + 'px' }"
        @touchstart="onTouchStart"
        @touchmove="onTouchMove"
        @touchend="onTouchEnd"
        @mousedown="onMouseDown"
      >
          <img :src="require(`../../assets/${platformIds}/add_icon.png`) " alt="" @click="showHidden = !showHidden">
      </div>
        <officialCode style="bottom:45px;" :visiable.sync="showHidden"></officialCode>
        <officialCode1 style="bottom:45px;" :visiable.sync="showHidden1"></officialCode1>
  </div>
</template>

<script>
import officialCode from '@/components/medicine/officialCode';
import officialCode1 from '@/components/newCommon/officialCode';
// import {  } from '@/api/';
export default {
  data () {
    return {
            platformIds:1,
            showHidden: false,
            showHidden1: false,
            isShowIcon:true,  // 默认是展示
            position: { x: 0, y: 0 },
            isDragging: false,
            dragStarted: false,
            startPosition: { x: 0, y: 0 },
            initialTouch: { x: 0, y: 0 },
            initialMouse: { x: 0, y: 0 },
            elementSize: { width: 80, height: 80 }
    };
  },
    watch: {
        $route: {
            handler (val) {
                this.platformIds = localStorage.getItem('platformId');
                // console.log('路由变化',val)
                if(val.name == 'targetPage' && val.params.pageId == 17){
                  this.isShowIcon = false
                }else{
                  this.isShowIcon = true
                }
            },
            deep: true,
            immediate: true
        }
    },

  created () { },

  components: {
		officialCode,
		officialCode1,
	},

  computed: {
   
  },

  mounted () {
    // 初始化位置到右下角
    this.initPosition();
    // 监听窗口大小变化
    window.addEventListener('resize', this.onWindowResize);
    window.addEventListener('orientationchange', this.onWindowResize);
  },

  beforeDestroy() {
    // 清理事件监听
    window.removeEventListener('resize', this.onWindowResize);
    window.removeEventListener('orientationchange', this.onWindowResize);
    document.removeEventListener('mousemove', this.onMouseMove);
    document.removeEventListener('mouseup', this.onMouseUp);
  },

  methods: {
    // 初始化位置
    initPosition() {
      this.position.x = window.innerWidth - this.elementSize.width - 8;
      this.position.y = window.innerHeight - this.elementSize.height - 100;
    },
    
    // 限制位置在屏幕范围内
    constrainPosition(x, y) {
      const maxX = window.innerWidth - this.elementSize.width;
      const maxY = window.innerHeight - this.elementSize.height;
      
      return {
        x: Math.max(0, Math.min(x, maxX)),
        y: Math.max(0, Math.min(y, maxY))
      };
    },

    // 触摸事件处理
    onTouchStart(event) {
      this.isDragging = false;
      this.dragStarted = false;
      const touch = event.touches[0];
      this.startPosition = {
        x: touch.clientX - this.position.x,
        y: touch.clientY - this.position.y
      };
      this.initialTouch = {
        x: touch.clientX,
        y: touch.clientY
      };
    },
    
    onTouchMove(event) {
      const touch = event.touches[0];
      const deltaX = Math.abs(touch.clientX - this.initialTouch.x);
      const deltaY = Math.abs(touch.clientY - this.initialTouch.y);
      
      // 只有当移动距离超过阈值时才开始拖拽
      if (deltaX > 10 || deltaY > 10) {
        if (!this.dragStarted) {
          this.dragStarted = true;
          this.isDragging = true;
          event.preventDefault();
        }
        
        if (this.isDragging) {
          event.preventDefault();
          const newPosition = this.constrainPosition(
            touch.clientX - this.startPosition.x,
            touch.clientY - this.startPosition.y
          );
          this.position = newPosition;
        }
      }
    },
    
    onTouchEnd(event) {
      if (this.isDragging) {
        event.preventDefault();
      }
      this.isDragging = false;
      this.dragStarted = false;
    },

    // 鼠标事件处理（PC端支持）
    onMouseDown(event) {
      this.isDragging = false;
      this.dragStarted = false;
      this.startPosition = {
        x: event.clientX - this.position.x,
        y: event.clientY - this.position.y
      };
      this.initialMouse = {
        x: event.clientX,
        y: event.clientY
      };
      
      document.addEventListener('mousemove', this.onMouseMove);
      document.addEventListener('mouseup', this.onMouseUp);
    },
    
    onMouseMove(event) {
      const deltaX = Math.abs(event.clientX - this.initialMouse.x);
      const deltaY = Math.abs(event.clientY - this.initialMouse.y);
      
      // 只有当移动距离超过阈值时才开始拖拽
      if (deltaX > 5 || deltaY > 5) {
        if (!this.dragStarted) {
          this.dragStarted = true;
          this.isDragging = true;
          event.preventDefault();
        }
        
        if (this.isDragging) {
          event.preventDefault();
          const newPosition = this.constrainPosition(
            event.clientX - this.startPosition.x,
            event.clientY - this.startPosition.y
          );
          this.position = newPosition;
        }
      }
    },
    
    onMouseUp(event) {
      if (this.isDragging) {
        event.preventDefault();
      }
      this.isDragging = false;
      this.dragStarted = false;
      
      document.removeEventListener('mousemove', this.onMouseMove);
      document.removeEventListener('mouseup', this.onMouseUp);
    },

    // 窗口大小变化处理
    onWindowResize() {
      // 延迟执行以确保获取到正确的窗口尺寸
      setTimeout(() => {
        const constrainedPosition = this.constrainPosition(this.position.x, this.position.y);
        this.position = constrainedPosition;
      }, 100);
    }
  }
}

</script>
<style  scoped  lang='scss'>
.addXiaoyi {
    position: fixed;
    height: 80px;
    width: 80px;
    text-align: center;
    z-index: 99;
    cursor: move;
    user-select: none;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    touch-action: none;
    
    img {
        // pointer-events: none;
        width: 100%;
        height: 100%;
        object-fit: contain;
    }
}
</style>
