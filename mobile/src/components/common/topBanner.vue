<template>
  <div v-if="swiperData.length" class="top-banner-box">
    <!-- 轮播图 -->
    <cube-slide class="swiper" :class="swiperClass" :circular="circularFlag" :autoPlay="!!interval" :data="swiperData"
      :interval="interval" @change="changePage" allowVertical>
      <cube-slide-item class="swiperItem-content" v-for="(item, index) in swiperData" :key="index"
        @click.native="clickImage(item)" :style="`background-image: url(${item.imgUrl})`">
        <img src="" alt="" style="display: none" />
      </cube-slide-item>
    </cube-slide>
    <div class="PreviewEnviroment" v-if="isPreviewEnviroment"></div>
  </div>
</template>

<script>
import { getAdvertList } from '@/api/medicine/homePage'
export default {
  props: ['configuration'],
  data() {
    return {
      autoplay: true,
      duration: 500,
      interval: 2000,
      swiperData: [],
      // 展示模式
      config: {
        displayNum: 1,
        listStyle: "rowOne",
      },
      pcConfig: {}, //pc端配置
    };
  },
  computed: {
    swiperClass() {
      if (this.config.mode == 1) {
        //pc样式
        return "swiper1";
      } else if (this.config.mode == 2) {
        //自定义样式
        return "swiper2";
      }
    },
  },
  watch: {
     configuration: {
            handler(val) {
                this.getSwiperData()
            },
            deep: true,
            immediate: true
        }
  },
  mounted() {
  
  },
  created() {
  },
  methods: {
    async getSwiperData() {

         let params = this.isPreviewEnviroment ? this.configuration.previewApiUrl : this.configuration.apiUrl;
         params = params + `&webPlatform=2`; 
         getAdvertList(params).then(res => {
                if (res.code == this.$successCode) {
                    this.swiperData = res.data
                    this.config = JSON.parse(this.configuration.param);
                    this.pcConfig = JSON.parse(this.configuration.pcParam); //pc端配置
                    this.interval = this.config.mode == 1 ? this.pcConfig.param * 1000 : this.config.param * 1000;
                } else {
                    this.$message.error(res.info);
                }
            })
    },
    clickImage(item) {
      if (item.targetUrl != '') {
                window.location.href = item.targetUrl
            }
            return
      }
  },
};
</script>

<style lang="scss" scoped>
.top-banner-box {
  width: 100%;

  .swiper {
    height: 20px;
    transform: translateY(0);
    overflow: hidden;

    &.swiper1 {
      height: 20px;
    }

    &.swiper2 {
      height: 84px;
    }
  .swiperItem-content {
      height: 100%;
      position: relative;
      background-size: cover;
      background-repeat: no-repeat;
      background-position: center;
      a {
          border: 0 !important;
        }
      
        img {
          border: 0 !important;
        }
    }

  }
  .PreviewEnviroment {
  position: absolute;
  width: 100%;
  height: 100%;
  background-color: #ff000000;
  top: 0;
}
}
</style>
