/**
 * 微信浏览器路由兼容性处理工具
 * 解决iOS微信浏览器中路由跳转和回退的兼容性问题
 * 
 * 主要解决的问题：
 * 1. iOS微信浏览器中使用$router.push跳转后，点击回退按钮会跳过中间页面
 * 2. 微信浏览器历史记录管理异常
 * 3. pushState和replaceState在微信浏览器中的兼容性问题
 */

class WechatRouterHelper {
  constructor() {
    this.isIOSWeChat = this.detectIOSWeChat();
    this.routeStack = []; // 自维护的路由栈
    this.maxStackSize = 50;
    
    //console.log('WechatRouterHelper初始化，iOS微信浏览器:', this.isIOSWeChat);
    
    // 初始化时记录当前路由
    if (typeof window !== 'undefined' && window.location) {
      this.routeStack.push({
        path: window.location.pathname + window.location.search,
        fullPath: window.location.href,
        timestamp: Date.now()
      });
    }
    
    // 监听浏览器的后退/前进事件
    if (this.isIOSWeChat) {
      this.setupPopstateListener();
    }
  }

  /**
   * 检测是否是iOS微信浏览器
   */
  detectIOSWeChat() {
    if (typeof navigator === 'undefined') return false;
    
    const ua = navigator.userAgent.toLowerCase();
    const isIOS = /iphone|ipad|ipod/.test(ua);
    const isWeChat = /micromessenger/.test(ua);
    
    return isIOS && isWeChat;
  }

  /**
   * 设置popstate事件监听器
   */
  setupPopstateListener() {
    if (typeof window === 'undefined') return;
    
    window.addEventListener('popstate', (event) => {
      //console.log('iOS微信浏览器popstate事件触发', {
      //  state: event.state,
      //  pathname: window.location.pathname,
      //  stackLength: this.routeStack.length
      //});
      
      // 在微信浏览器中，我们可以在这里进行额外的处理
      this.handlePopstate(event);
    });
  }

  /**
   * 处理popstate事件
   */
  handlePopstate(event) {
    try {
      const currentPath = window.location.pathname + window.location.search;
      
      // 检查当前路径是否在我们的栈中
      const stackIndex = this.routeStack.findIndex(item => item.path === currentPath);
      
      if (stackIndex !== -1) {
        // 如果找到了，更新栈到对应位置
        this.routeStack = this.routeStack.slice(0, stackIndex + 1);
        //console.log('更新路由栈到索引:', stackIndex);
      }
    } catch (error) {
      //console.warn('处理popstate事件时出错:', error);
    }
  }

  /**
   * 记录路由到栈中
   */
  pushToStack(route) {
    const routeInfo = {
      path: typeof route === 'string' ? route : (route.fullPath || route.path),
      fullPath: typeof route === 'string' ? route : (route.fullPath || route.path),
      timestamp: Date.now(),
      meta: typeof route === 'object' ? route.meta : {}
    };
    
    // 避免重复记录相同路由
    const lastRoute = this.routeStack[this.routeStack.length - 1];
    if (!lastRoute || lastRoute.path !== routeInfo.path) {
      this.routeStack.push(routeInfo);
      
      // 限制栈大小
      if (this.routeStack.length > this.maxStackSize) {
        this.routeStack.shift();
      }
      
    //   console.log('路由栈更新:', {
    //     current: routeInfo.path,
    //     stackLength: this.routeStack.length
    //   });
    }
  }

  /**
   * 兼容性路由跳转 - 核心方法
   */
  push(router, location) {
    if (!this.isIOSWeChat) {
      // 非iOS微信浏览器，使用默认行为
      return router.push(location);
    }

    //console.log('iOS微信浏览器路由跳转:', location);

    // iOS微信浏览器特殊处理
    try {
      // 记录当前路由到栈中
      this.pushToStack(router.currentRoute);
      
      // 关键解决方案：在iOS微信浏览器中使用replace替代push
      // 但需要先手动管理历史记录
      
      // 方案：使用pushState手动添加历史记录，然后使用replace跳转
      const targetPath = this.getTargetPath(location);
      
      // 手动添加一个历史记录项
      if (window.history && window.history.pushState) {
        const currentState = window.history.state;
        const newState = { 
          ...currentState, 
          wechatRouterManaged: true,
          timestamp: Date.now()
        };
        
        // 先push一个状态，这样回退时就有正确的历史记录
        window.history.pushState(newState, '', window.location.href);
      }
      
      // 然后使用Vue Router的replace方法跳转
      // 这样可以避免微信浏览器的历史记录管理问题
      return router.replace(location).then(() => {
        // 跳转成功后，更新我们的路由栈
        this.pushToStack(router.currentRoute);
        //console.log('iOS微信浏览器路由跳转成功');
      }).catch(err => {
        // 如果replace失败，回退到push方法
        if (err.name !== 'NavigationDuplicated') {
         // console.warn('replace失败，回退到push:', err);
          return router.push(location);
        }
        return Promise.resolve();
      });
      
    } catch (error) {
      //console.warn('微信浏览器路由跳转出错，使用默认行为:', error);
      return router.push(location);
    }
  }

  /**
   * 获取目标路径
   */
  getTargetPath(location) {
    if (typeof location === 'string') {
      return location;
    }
    
    if (location.path) {
      return location.path + (location.query ? '?' + new URLSearchParams(location.query).toString() : '');
    }
    
    if (location.name) {
      return `/${location.name}`;
    }
    
    return '/';
  }

  /**
   * 兼容性路由替换
   */
  replace(router, location) {
    if (!this.isIOSWeChat) {
      return router.replace(location);
    }
    
    // iOS微信浏览器中，replace行为相对稳定
    return router.replace(location).then(() => {
      // 更新路由栈
      if (this.routeStack.length > 0) {
        this.routeStack[this.routeStack.length - 1] = {
          path: router.currentRoute.fullPath,
          fullPath: router.currentRoute.fullPath,
          timestamp: Date.now(),
          meta: router.currentRoute.meta || {}
        };
      }
    });
  }

  /**
   * 获取路由栈
   */
  getRouteStack() {
    return [...this.routeStack];
  }

  /**
   * 清理路由栈
   */
  clearRouteStack() {
    this.routeStack = [];
  }

  /**
   * 检查是否可以回退
   */
  canGoBack() {
    return this.routeStack.length > 1;
  }
}

// 创建单例实例
const wechatRouterHelper = new WechatRouterHelper();

/**
 * 兼容性路由跳转函数
 */
export function compatibleRouterPush(router, location) {
  return wechatRouterHelper.push(router, location);
}

/**
 * 兼容性路由替换函数
 */
export function compatibleRouterReplace(router, location) {
  return wechatRouterHelper.replace(router, location);
}

/**
 * 检测是否是iOS微信浏览器
 */
export function isIOSWeChat() {
  return wechatRouterHelper.isIOSWeChat;
}

/**
 * 获取路由栈信息
 */
export function getRouteStack() {
  return wechatRouterHelper.getRouteStack();
}

/**
 * Vue插件安装函数
 */
export function install(Vue) {
  // 在Vue原型上添加兼容性方法
  Vue.prototype.$compatibleRouterPush = function(location) {
    return compatibleRouterPush(this.$router, location);
  };
  
  Vue.prototype.$compatibleRouterReplace = function(location) {
    return compatibleRouterReplace(this.$router, location);
  };
  
  Vue.prototype.$isIOSWeChat = isIOSWeChat();
  
  Vue.prototype.$getRouteStack = function() {
    return getRouteStack();
  };
  
  // 添加全局mixin，在所有组件中提供调试信息
  Vue.mixin({
    created() {
      if (this.$isIOSWeChat && this.$options.name) {
        //console.log(`组件 ${this.$options.name} 在iOS微信浏览器中创建`);
      }
    }
  });
}

export default wechatRouterHelper; 