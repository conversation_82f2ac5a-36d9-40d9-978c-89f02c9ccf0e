import CONSTANT from "../config/config_constant";
import { compatibleRouterPush, isIOSWeChat } from "./wechatRouter";

export default function (Router) {
    const originalPush = Router.prototype.push;
    const originalReplace = Router.prototype.replace;
    
    //改写原有的replace 和 push 使其携带固定参数，并处理微信浏览器兼容性
    
    Router.prototype.replace = function replace(location) {
        addRouterInfo(location);
        return originalReplace.call(this, location).catch(err => err)
    }
    
    Router.prototype.push = function push(location) {
        addRouterInfo(location);
        
        // iOS微信浏览器使用兼容性处理
        if (isIOSWeChat()) {
            //console.log('检测到iOS微信浏览器，使用兼容性路由跳转');
            return compatibleRouterPush(this, location);
        }
        
        // 其他环境使用原有逻辑
        return originalPush.call(this, location).catch(err => err)
    }

}

function addRouterInfo(location) {
    if (localStorage.getItem(CONSTANT.USERINFO)) {
        let platformInfo = JSON.parse(localStorage.getItem(CONSTANT.USERINFO));
        if (!location.query) {
            location.query = {};
        }
        location.query.platformId = platformInfo.platformId;
        location.query.bizId = platformInfo.bizId;
        location.query.domainType = platformInfo.domainType;
    }
}


