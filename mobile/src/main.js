import "minireset.css";
import "@/assets/css/base.scss";
import Vue from "vue";
import App from "./App.vue";
import router from "./router";
import installDirective from "./directives";
import plugins from "./plugins";
import store from "./store";
import "amfe-flexible";
import "babel-polyfill";
import "./utils/rem.js";
import Cube from "cube-ui";
Vue.use(Cube);
import vant from "vant";
Vue.use(vant);
// 引入公用方法
import { globalMins, loginPage } from "enmore_common_mobile";
Vue.mixin(globalMins);
Vue.component("loginPage", loginPage);
import HavevConsole from "@/utils/vconsole";
HavevConsole();
//引入mixin
import mixin from "./mixin/index";
Vue.mixin(mixin);
//注册所有全局指令
installDirective(Vue);
//注册插件
Object.keys(plugins).forEach((key) => {
  plugins[key].install(Vue);
});

// 注册微信浏览器路由兼容性插件
import { install as wechatRouterInstall } from './utils/wechatRouter';
Vue.use(wechatRouterInstall);

Vue.config.productionTip = false;
window.vm = new Vue({
  router,
  store,
  render: (h) => h(App),
}).$mount("#app");

import { dataActionMixin } from "@enmore/common-data-action";
Vue.mixin(dataActionMixin);
