import moment from "moment";
import router from "@/router";
import { addClickTagNumByRelIdy } from "../api/configurable/home";

import { systemUrl } from "../config/env";
var platformId = localStorage.getItem("platformId");
var bizId = localStorage.getItem("bizId");
import CONSTANT from "../config/config_constant";

const TYPE = {
  0: "activity",
  1: "course",
  2: "lecturer",
  3: "audio",
  4: "thinktank",
  5: "info",
  6: "live",
  7: "topic",
  8: "diy",
  9: "series",
  10: "product",
};
const pageMenuList = {
  //手机端：pc端对应菜单id
  10: 1,
  11: 2,
  601: 501,
  602: 502,
};
export default {
  data() {
    return {
      SquareImgSuffix: "?x-oss-process=image/resize,m_pad,w_300,h_300,limit_0,color_ffffff",
      TYPE: TYPE,
      isPreviewEnviroment: process.env.VUE_APP_CURRENTMODE.indexOf("Preview") > -1 ? true : false, // 是不是预览的环境
      pageMenuList: pageMenuList,
      currentPlatformId: platformId,
    };
  },
  computed: {
    qrCodeUrl() {
      if (localStorage.getItem(CONSTANT.USERINFO)) {
        return JSON.parse(localStorage.getItem(CONSTANT.USERINFO)).platformLogo;
      }
    },

    systemStyle() {
      try {
        return JSON.parse(localStorage.getItem("officialMenu")).data[0].extraInfo.style;
      } catch (e) {
        return null;
      }
    },
    // 侧边栏信息
    sliderInfoStatus() {
      return this.$store.state.sliderInfoStatus;
    },
  },
  watch: {},

  created() {},
  mounted() {},
  methods: {
    //跳转列表的方法,老方法,目前建议跳转通用列表
    naviToList(type, homeType = -1, targetUrl, courseType = null) {
      let baseUrl;
      switch (type) {
        case TYPE[1]:
          this.naviToCommonList({
            type: type,
            courseType: courseType,
          });
          break;
        case TYPE[2]:
          window.location.href = baseUrl + `bbs/#/lecturer?platformId=${platformId}&bizId=${bizId}`;
          break;
        case TYPE[4]:
          window.location.href = baseUrl + `bbs/#/thinkTank?platformId=${platformId}&bizId=${bizId}`;
          break;
        case TYPE[5]:
          this.naviToCommonList({
            type: type,
            homeType: homeType,
          });
          break;
        case TYPE[7]:
          window.location.href = baseUrl + `bbs/#/topics?platformId=${platformId}&bizId=${bizId}`;
          break;
        case TYPE[8]:
          //自定义链接
          window.location.href = targetUrl;
          break;
        default:
          this.naviToCommonList({
            type: type,
          });
          break;
      }
    },
    //跳转通用列表的方法
    naviToCommonList(params, mode = "common") {
      //目前mode 为common 或live ,这将决定内部的item样式
      if (params.position) {
        //通用列表过滤掉首页展示等条件
        delete params.position;
      }
      params.platformId = platformId;
      params.bizId = bizId;
      router.push({
        name: "commonList",
        params: {
          mode,
        },
        query: params,
      });
    },
    //跳转详情的方法
    naviToDetails(platformId, type, item) {
      let baseUrl, liveUrl, productUrl;
      // if (process.env.VUE_APP_CURRENTMODE == 'development' || process.env.VUE_APP_CURRENTMODE == 'test') {
      //     //这里修改环境的前缀
      //     baseUrl = location.origin + '/web/';
      //     liveUrl = location.origin + '/mobile/#/';
      //     productUrl = location.origin + '/exhibitor/#/';
      // } else {
      //     baseUrl = 'https://live.cnhangjia.com/info/'
      //     liveUrl = 'https://live.cnhangjia.com/mobile/#/'
      //     productUrl = 'http://live.cnhangjia.com/exhibitor/#/'
      // }

      baseUrl = location.origin + "/web/";
      liveUrl = location.origin + "/mobile/#/";
      productUrl = location.origin + "/exhibitor/#/";

      if (item.relId) {
        addClickTagNumByRelIdy({
          relId: item.relId,
        }).then(jump(type, item));
      } else {
        jump(type, item);
      }

      function jump(type, item) {
        switch (type) {
          case TYPE[0]:
            // 会议详情 跳转iframe页面 (我的收藏列表传入活动id为itemId)
            if (item.urlType == 99) {
              window.location.href = item.conferenceUrl;
            } else {
              location.assign(`${window.location.origin}${window.location.pathname}#/activityIframe?activityId=${item.itemId}&source=${item.source}&bizId=${item.bizId}`);
            }
            break;
          case TYPE[1]:
            if (item.courseType === 0) {
              window.location.href = baseUrl + `course/#/courseDetail/${item.id}?platformId=${platformId}&bizId=${item.bizId}&domainType=1`;
            } else {
              window.location.href = baseUrl + `course/#/listeningDetail/${item.id}?platformId=${platformId}&bizId=${item.bizId}&domainType=1`;
            }
            break;
          case TYPE[2]:
            window.location.href = baseUrl + `bbs/#/lecturer/detail/${item.id}?platformId=${platformId}&bizId=${item.bizId}&domainType=1`;
            break;

          case TYPE[4]:
            window.location.href = baseUrl + `bbs/#/tt_details/${item.id}?platformId=${platformId}&bizId=${item.bizId}&domainType=1`;
            break;
          case TYPE[5]:
            // 跳转本地列表
            window.location.href = baseUrl + `bbs/#/com_artcleDetail/${item.id}?platformId=${platformId}&bizId=${item.bizId}&domainType=1`;
            break;
          case TYPE[6]:
            window.location.href =  liveUrl + `zh-cn/liveDetail?liveId=${item.itemId ? item.itemId : item.id}&platformId=${platformId}&bizId=${item.bizId}&domainType=1`;
            break;
          case TYPE[7]:
            window.location.href = baseUrl + `bbs/#/topticsDetail/${item.id}?platformId=${platformId}&bizId=${item.bizId}&domainType=1`;
            break;
          case TYPE[8]:
            if (item.targetUrl || item.topUrl) {
              window.location.href = item.targetUrl || item.topUrl;
            }
            break;
          case TYPE[9]:
            window.location.href = liveUrl + `zh-cn/seriesLive/${item.id}?platformId=${platformId}&bizId=${item.bizId}&domainType=1`;
            break;
          case TYPE[10]:
            // 产品详情
            window.location.href = productUrl + `?productId=${item.itemId}&lang=0&platformId=${platformId}&liveId=&bizId=${item.bizId}&domainType=1`;
            break;
          default:
            console.warn("无对应类型");
            break;
        }
      }
    },
    formatTime(time, type = "date") {
      if (type == "date" || type === true) {
        return moment(time).format("YYYY/MM/DD");
      } else if (type == "dateTime" || type === false) {
        return moment(time).format("YYYY/MM/DD HH:mm");
      } else if (type == "noYear") {
        return moment(time).format("MM/DD");
      } else if (type == "activity") {
        return moment(time).format("M-DD HH:mm");
      } else {
        return moment(time).format(type);
      }
    },

    formatDuring(mss) {
      var days = parseInt(mss / (1000 * 60 * 60 * 24));
      var hours = parseInt((mss % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
      var minutes = parseInt((mss % (1000 * 60 * 60)) / (1000 * 60));
      var seconds = (mss % (1000 * 60)) / 1000;
      if (days > 0) {
        return days + "天" + hours + "时" + minutes + "分" + seconds + "秒";
      } else if (hours > 0) {
        return hours + "时" + minutes + "分" + seconds + "秒";
      } else if (minutes > 0) {
        return minutes + "分" + seconds + "秒";
      } else {
        return seconds + "秒";
      }
    },

    // 动态表单使用
    moment(time, type, format, date) {
      if (time) {
        if (type) {
          return moment(time).format("YYYY-MM-DD");
        } else {
          if (format) {
            if (date) {
              return moment(time).format("YYYY/MM/DD");
            } else {
              return moment(time).format("YYYY/MM/DD HH:mm");
            }
          } else {
            return moment(time).format("YYYY-MM-DD HH:mm:ss");
          }
        }
      } else {
        return "";
      }
    },

    formatTag(text, N = 4) {
      if (text) {
        return text.length > N + 1 ? text.substring(0, N) + "…" : text;
      } else {
        return null;
      }
    },

    formatAddress(item) {
      if (item.provinceCode && item.cityCode) {
        if (["110000", "120000", "310000", "990000", "500000"].includes(item.provinceCode)) {
          //直辖市
          return item.city;
        } else {
          //正常城市
          // return item.province + "-" + item.city
          return item.city;
        }
      } else {
        //两者有一个或者都无
        return item.city || item.province;
      }
    },

    ifshowTag(tags, index) {
      let tagLength = 0;
      tags.forEach((item, itemIndex) => {
        if (itemIndex < 3 && tags[itemIndex]) {
          tagLength += item.tagName.length;
        }
      });
      if (index < 3) {
        if (tagLength < 14) {
          return true;
        } else {
          if (index < 2) {
            return true;
          } else {
            return false;
          }
        }
      } else {
        return false;
      }
    },

    loginShowFunction() {
      this.$store.state.loginShow = true;
      localStorage.setItem("courseType", null);
      localStorage.setItem("courseItemId", null);
      localStorage.setItem("registerChannel", 2);
    },
    // 微信分享配置
    wxShareConfig(newShareConfig, successFnType, params) {
      let title = newShareConfig.shareTitle || (this.shareInfo && this.shareInfo.shareTitle) || "";
      let desc = newShareConfig.shareDesc || (this.shareInfo && this.shareInfo.shareDesc) || "";
      let imgUrl = (newShareConfig.shareImgUrl || (this.shareInfo && this.shareInfo.shareImgUrl)) + "?x-oss-process=image/resize,m_pad,w_300,h_300,limit_0,color_ffffff";
      let link = location.href.includes("isShare") ? location.href : location.href + "&isShare=1";

      function success() {
        // console.log('设置成功')
        if (successFnType == "pageDataAction") {
          // 分享后 埋点
          params.that.pageDataAction(params.clickEle, params.currentPage, params.targetPage, params.businessId, params.queryParam, params.businessType, params.businessSeq);
        }
      }

      function fail() {
        // console.log('设置失败')
      }
      wx.ready(() => {
        wx.updateTimelineShareData({
          title: title,
          desc: desc,
          link: link,
          imgUrl: imgUrl,
          success: success,
          fail: fail,
        });
        wx.updateAppMessageShareData({
          title: title,
          desc: desc,
          link: link,
          imgUrl: imgUrl,
          success: success,
          fail: fail,
        });
        // “分享给朋友”
        wx.onMenuShareAppMessage({
          title: title,
          desc: desc,
          link: link,
          imgUrl: imgUrl,
          success: success,
          fail: fail,
        });
        // “分享到朋友圈”
        wx.onMenuShareTimeline({
          title: title,
          link: link,
          imgUrl: imgUrl,
          success: success,
          fail: fail,
        });

        // 分享到QQ
        wx.onMenuShareQQ({
          title: title,
          desc: desc,
          link: link,
          imgUrl: imgUrl,
          success: success,
          fail: fail,
        });
        // 分享到腾讯微博
        wx.onMenuShareWeibo({
          title: title,
          desc: desc,
          link: link,
          imgUrl: imgUrl,
          success: success,
          fail: fail,
        });
        // 分享到QQ空间
        wx.onMenuShareQZone({
          title: title,
          desc: desc,
          link: link,
          imgUrl: imgUrl,
          success: success,
          fail: fail,
        });
      });
    },

    //根据指定key和value从对象中搜索指定对象
    getTargetObjectWhereAttrIs(dataObj, attrKey, attrValue) {
      var tempData = null;
      fn(dataObj, attrKey, attrValue);

      function fn(dataObj, attrKey, attrValue) {
        Object.keys(dataObj).map((key) => {
          let item = dataObj[key];
          if (item && item[attrKey] == attrValue) {
            tempData = item;
          } else if (item instanceof Object) {
            fn(item, attrKey, attrValue);
          } else {
          }
        });
      }
      return tempData;
    },
    // c端向超管设置发送数据
    sendMessage(sendData) {
      window.parent.postMessage(JSON.stringify(sendData), systemUrl + "/#/medicineIndexMobile");
    },
  },
};
