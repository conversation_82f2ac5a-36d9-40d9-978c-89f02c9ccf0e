import Axios from '@/plugins/http'
//查看布局信息

let getOfficialMenuMedicine;
if (process.env.VUE_APP_CURRENTMODE.indexOf('Preview')>-1) { // 预览环境
  getOfficialMenuMedicine = (params) => Axios(3).get(`/layout/mobile/selectLayoutByMenu/${params.menu}`, {params})
} else {
  getOfficialMenuMedicine = (params) => Axios(3).get(`/front/layout/select/${params.menu}`, {params})
}
// 活动下的自定义
let getHomePageItemList1
if (process.env.VUE_APP_CURRENTMODE.indexOf('Preview')>-1) { // 预览环境
  getHomePageItemList1 = (params) => Axios(3).get(`/layout/getHomePageItemList/${params.layoutId}`, {params})
} else {
  getHomePageItemList1 = (params) => Axios(3).get(`/front/layout/getHomePageItemList/${params.layoutId}`, {params})
}
// 查询
const getList = (url, params) => {
	return Axios(3).get(url, {
		params
	})
}

// 查询banner图片列表
const getBannerList = (url, params) => {
	return Axios(3).get(url, {
		params
	})
}

// 查询金刚区列表
const getIconList = (url, params) => {
	return Axios(3).get(url, {
		params
	})
}

// 查询advert(腰封广告)列表
const getAdvertList = (url, params) => {
	return Axios(3).get(url, {
		params
	})
}

// 请求（直播，列表）
const getConferenceFront = (url, params) => {
	return Axios(3).get(url, {
		params
	})
}
// 查询carousel 轮播咨询列表
const getCarouselList = (url, params) => {
	return Axios(3).get(url, {
		params
	})
}
// 查询会议列表
const getActivity = (url, params) => {
	return Axios(3).get(url, {
		params
	})
}

//  获取 会议活动   分类
const getClassify = (system) => Axios(9).get(`/common/codevalue/${system}`)
// 获取公司类型级联数据
const getClassComify = (system) => Axios(1).get(`/common/codevalue/${system}`)

// 获取直播的分类
const getClassifyLive = (url, params) => Axios(9).get(url, {
	params
});

// 查询自定义列表
const getHomePageItemList = (url, params) => {
	return Axios(3).get(url, {
		params
	})
}
// 查询自定义列表
const getLiveInProgressList = (url, params) => {
	return Axios(3).get(url, {
		params
	})
}


// 直播的搜索
const getSearchList = (params) => Axios(3).get(`/live/front/list/${params.platformId}`,{params});

// 自定义模块的搜索
const selectMoreItemList = (params) => Axios(3).get(`front/layout/getActivityLiveMore`,{params});

// 获取  关于易贸医疗 信息
const getAboutMedicine = (relId) => Axios(10).get(`biz/official/${relId}`);

// 查看首页微信分享
const getHomeShare = (params) => Axios(3).get(`/layout/home/<USER>/front/select/${params.type}`,{params});

// 查看侧边栏的相关配置
const getSliderStatus = (params) => Axios(3).get(`/layout/home/<USER>/front/config`);


// 查看配置的状态
const getSelectConfig = (params) => Axios(3).get(`/front/layout/selectConfig/${params.type}`);

// 获取ebc跳转链接
const getEBCLink = () => Axios(3).get(`/front/layout/ebc`);

// 判断金刚区内 模块是否开启
const getKingKongItemStatus = (id) => Axios(3).get(`/front/layout/checkFlag/${id}`);

// 直播模块 加载更多
let selectLoadingItemList
if (process.env.VUE_APP_CURRENTMODE.indexOf('Preview')>-1) { // 预览环境
  selectLoadingItemList = (params) => Axios(3).get(`/layout/selectLoadingItemList/${params.layoutId}`, {params})
} else {
  selectLoadingItemList = (params) => Axios(3).get(`/front/layout/selectLoadingItemList/${params.layoutId}`, {params})
}

// 直播模块 加载更多
let selectLoadingItemActivityList
if (process.env.VUE_APP_CURRENTMODE.indexOf('Preview')>-1) { // 预览环境
  selectLoadingItemActivityList = (params) => Axios(3).get(`/layout/selectLoadingItemActivityList/${params.layoutId}`, {params})
} else {
  selectLoadingItemActivityList = (params) => Axios(3).get(`/front/layout/selectLoadingItemActivityList/${params.layoutId}`, {params})
}

// 获取是否下拉加载
let getInfiniteLoad
if (process.env.VUE_APP_CURRENTMODE.indexOf('Preview')>-1) { // 预览环境
  getInfiniteLoad = (params) => Axios(3).get(`/layout/mobile/infiniteLoad/${params.menu}`, {params})
} else {
  getInfiniteLoad = (params) => Axios(3).get(`/front/layout/infiniteLoad/${params.menu}`, {params})
}
// 获取活动列表，
let getHomePageActivityList
if (process.env.VUE_APP_CURRENTMODE.indexOf('Preview')>-1) { // 预览环境
  getHomePageActivityList = (params) => Axios(3).get(`/layout/getHomePageActivityList/${params.layoutId}`, {params})
} else {
  getHomePageActivityList = (params) => Axios(3).get(`/front/layout/getHomePageActivityList/${params.layoutId}`, {params})
}

// 获取自定义活动列表，
let getCustomLiveActivityRel
if (process.env.VUE_APP_CURRENTMODE.indexOf('Preview')>-1) { // 预览环境
  getCustomLiveActivityRel = (params) => Axios(3).get(`/layout/custom/getCustomLiveActivityRel/${params.layoutId}`, {params})
} else {
  getCustomLiveActivityRel = (params) => Axios(3).get(`/front/layout/custom/getCustomLiveActivityRel/${params.layoutId}`, {params})
}

// 获取自定义图文列表
let getCustomImageTextInfo
if (process.env.VUE_APP_CURRENTMODE.indexOf('Preview')>-1) { // 预览环境
  getCustomImageTextInfo = (params) => Axios(3).get(`layout/home/<USER>/getList/${params.layoutId}`, {params})
} else {
  getCustomImageTextInfo = (params) => Axios(3).get(`layout/home/<USER>/customImageTextInfo/getList/${params.layoutId}`, {params})
}

// 获取短视频详情
const getShortVideoDetail = (params) => Axios(3).get(`/conference/front/shortVideo/${params.id}`)


// 获取榜单数据
const getLeaderBoardsList = (params) => Axios(3).get(`/front/layout/rank-detail/${params.rankId}`)


// 获取职能banner 主图 
const getFunClassifyBanner = (params) => Axios(3).get(`/layout/getLiveClassifyTag/${params.subMenu}`,{params})


// 查询行业分类 有些暂不显示的 需要通过接口获得
const getIndustryClassify = (params) => Axios(3).get(`/front/layout/getLiveChannelList/${params.layoutId}`,{params})


export {
	getOfficialMenuMedicine,
	getList,
	getBannerList,
	getIconList,
	getAdvertList,
	getConferenceFront,
	getCarouselList,
	getActivity,
	getClassify,
	getClassifyLive,
	getLiveInProgressList,
	getSearchList,
	selectMoreItemList,
	getAboutMedicine,
	getHomeShare,
	getSliderStatus,
	getSelectConfig,
	getEBCLink,
	getKingKongItemStatus,
	getHomePageItemList,
	getHomePageItemList1,
	selectLoadingItemList,
	selectLoadingItemActivityList,
	getInfiniteLoad,
	getHomePageActivityList,
	getCustomLiveActivityRel,
	getCustomImageTextInfo,
	getClassComify,
	getShortVideoDetail,
	getLeaderBoardsList,
	getFunClassifyBanner,
	getIndustryClassify,
}
